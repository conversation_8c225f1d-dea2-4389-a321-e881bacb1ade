package com.wacai.loan.goblin.tenant.core;

import com.wacai.gbl.jasmine.annotation.JasmineValue;
import com.wacai.loan.cache.Cache;
import com.wacai.loan.goblin.common.config.jasmine.JasmineConfig;
import com.wacai.loan.goblin.common.constant.DataStatus;
import com.wacai.loan.goblin.tenant.api.AgencyService;
import com.wacai.loan.goblin.tenant.api.AgentService;
import com.wacai.loan.goblin.tenant.api.dto.AgencyDTO;
import com.wacai.loan.goblin.tenant.api.dto.AgentDTO;
import com.wacai.loan.goblin.tenant.core.converter.AgentConverter;
import com.wacai.loan.goblin.tenant.dao.po.Agent;
import com.wacai.loan.goblin.tenant.dao.repository.AgentRepository;
import com.wacai.loan.tenant.spring.bean.annotation.TenantConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2017/5/10
 */
@Slf4j
@Service
@TenantConfig
public class DefaultAgentService implements AgentService {

    @Autowired
    private AgentRepository agentRepository;

    @Autowired
    private AgencyService agencyService;

//    @JasmineValue(key = "goblin.agent.loopDeep", defaultValue = "4", subModule = JasmineConfig.GOBLIN_SERVICE, probe = true)
    private int loopDeep = 4;
    private int loopSuperiorDeep = 4;

    @Override
    public List<AgentDTO> findAll() {
        List<Agent> agents = agentRepository.findAll();
        return AgentConverter.toAgentDTOS(agents);
    }

    @Override
    public List<AgentDTO> findAllActive() {
        List<Agent> agents = agentRepository.getListByActiveOrderByAccount(true);
        List<AgentDTO> agentDTOS = new ArrayList<>();
        for (Agent agent : agents) {
            agentDTOS.add(AgentConverter.toDTO(agent));
        }
        return agentDTOS;
    }

    @Override
//    @Cache(name = "agent")
    public AgentDTO findByAccount(String account) {
        Agent agent = agentRepository.findByAccount(account);
        return AgentConverter.toDTO(agent);
    }

    @Override
    public AgentDTO findByAccountActive(String account) {
        Agent agent = agentRepository.findByAccountAndActive(account, DataStatus.ACTIVE);
        return AgentConverter.toDTO(agent);
    }
    @Override
    public List<AgentDTO> findAgentsByAccountActive(List<String> accounts) {
        List<Agent> agents = agentRepository.findListByAccountInAndActive(accounts, DataStatus.ACTIVE);
        return AgentConverter.toDTOs(agents);
    }

    @Override
    public List<AgentDTO> findSubordinateByAccount(String account) {
        Agent agent = agentRepository.findByAccount(account);
        if (agent == null) {
            log.error("findSubordinateByAccount : can not found agent info by account [{}]", account);
            return Collections.emptyList();
        }
        List<AgentDTO> agentAllDTOs = new ArrayList<AgentDTO>();
        int loopDeep = this.loopDeep;
        //递归填充数据
        fillAgentDTOs(agentAllDTOs, agent.getId(), loopDeep);
        return agentAllDTOs;
    }


    @Override
    public List<AgentDTO> findSuperiorByAccount(String account) {
        Agent agent = agentRepository.findByAccount(account);
        if (agent == null) {
            log.error("findSubordinateByAccount : can not found agent info by account [{}]", account);
            return Collections.emptyList();
        }
        List<AgentDTO> agentAllDTOs = new ArrayList<AgentDTO>();
        int loopDeep = this.loopSuperiorDeep;
        //递归填充数据
        fillSuperiorAgentDTOs(agentAllDTOs, agent.getSuperiorId(), loopDeep);
        return agentAllDTOs;
    }

    @Override
    public List<AgentDTO> findOneLevelSubordinatesByAgentId(Long agentId) {
        List<Agent> list = agentRepository.findBySuperiorIdAndActive(agentId, DataStatus.ACTIVE);
        if (list == null) {
            return Collections.emptyList();
        }
        return AgentConverter.toAgentDTOS(list);
    }

    @Override
    public int findSubordinateCountByAccount(String account) {
        Agent agent = agentRepository.findByAccount(account);
        if (agent == null) {
            log.error("findSubordinateCountByAccount : can not found agent info by account [{}]", account);
            return 0;
        }
        return agentRepository.countBySuperiorIdAndActive(agent.getId(), DataStatus.ACTIVE);
    }

    private void fillSuperiorAgentDTOs(List<AgentDTO> agentAllDTOs, Long superiorId, int loopDeep) {
        if (loopDeep > 0) {
            Agent agent = agentRepository.findByIdAndActive(superiorId, DataStatus.ACTIVE);
            if (Objects.nonNull(agent)) {
                AgentDTO agentDTO = AgentConverter.toDTO(agent);
                agentAllDTOs.add(agentDTO);
                fillSuperiorAgentDTOs(agentAllDTOs, agent.getSuperiorId(), loopDeep - 1);
            }

        }
    }
    private void fillAgentDTOs(List<AgentDTO> agentAllDTOs, Long superiorId, int loopDeep) {
        if (loopDeep > 0) {
            List<Agent> list = agentRepository.findBySuperiorIdAndActive(superiorId, DataStatus.ACTIVE);
            if (list != null && list.size() > 0) {
                for (Agent agent : list) {
                    AgentDTO agentDTO = AgentConverter.toDTO(agent);
                    agentAllDTOs.add(agentDTO);
                    fillAgentDTOs(agentAllDTOs, agent.getId(), loopDeep - 1);
                }
            }
        }
    }

    @Override
    public AgentDTO getById(Long id) {
        Agent agent = agentRepository.getById(id);
        return AgentConverter.toDTO(agent);
    }

    @Override
    public AgentDTO save(AgentDTO agentDTO) {
        Agent agent = AgentConverter.toPO(agentDTO);
        agent = agentRepository.save(agent);
        return AgentConverter.toDTO(agent);
    }

    @Override
    public List<AgentDTO> findByAgencyIds(List<Long> agencyIds) {
        if (agencyIds == null || agencyIds.size() == 0) {
            return Collections.emptyList();
        }
        List<Agent> agents = agentRepository.findByActiveAndAgencyIdIn(DataStatus.ACTIVE, agencyIds);
        if (agents == null) {
            return Collections.emptyList();
        }
        return AgentConverter.toAgentDTOS(agents);
    }

    @Override
    public String getAgencyShortNameById(Long id) {
        Agent agent = agentRepository.getById(id);
        if (agent == null) {
            log.error("getAgencyShortNameById : cant not fount agent by id [{}]", id);
            return null;
        }
        Long agencyId = agent.getAgencyId();
        if (agencyId == null) {
            log.error("getAgencyShortNameById param agencyId is null");
            return null;
        }
        AgencyDTO agencyDTO = agencyService.queryAgencyById(agencyId);
        if (agencyDTO == null) {
            log.error("getAgencyShortNameById : cant not fount agencyDTO by agencyId [{}]", agencyId);
            return null;
        }
        return agencyDTO.getShortName();
    }

    @Override
    public List<AgentDTO> findSubordinateById(Long id) {
        List<AgentDTO> agentAllDTOs = new ArrayList<AgentDTO>();
        int loopDeep = this.loopDeep;
        //递归填充数据
        fillAgentDTOs(agentAllDTOs, id, loopDeep);
        return agentAllDTOs;
    }

    @Override
    public List<AgentDTO> findByIds(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyList();
        }
        List<Agent> agents = agentRepository.findListByIdInAndActive(agentIds, DataStatus.ACTIVE);
        return AgentConverter.toAgentDTOS(agents);
    }

    @Override
    public AgentDTO findLeaderByAccount(String account) {
        Agent agent = agentRepository.findByAccount(account);
        if (agent == null) {
            log.error("findLeaderByAccount : cant not found agent by account :{}", account);
            return null;
        }
        Agent leager = agentRepository.getById(agent.getSuperiorId());
        return AgentConverter.toDTO(leager);
    }

    @Override
    public List<AgentDTO> findByAgencyId(Long agencyId) {
        List<Agent> agents = agentRepository.findByAgencyIdAndActive(agencyId, DataStatus.ACTIVE);
        if (CollectionUtils.isEmpty(agents)) {
            return Collections.emptyList();
        }
        return AgentConverter.toAgentDTOS(agents);
    }

    @Override
    public List<AgentDTO> findGroupLeadersByAgencyId(Long agencyId) {

        List<AgentDTO> agentDTOS = findByAgencyId(agencyId);
        List<Long> groupLeaderIds = agentDTOS.stream()
                .filter(agent -> agent.getSuperiorId() != null)
                .map(AgentDTO::getSuperiorId)
                .distinct()
                .collect(Collectors.toList());

        return findByIds(groupLeaderIds).stream()
                .filter(agentDTO -> agentDTO.getAgencyId() != null &&
                        agentDTO.getAgencyId().equals(agencyId))
                .collect(Collectors.toList());
    }

}
