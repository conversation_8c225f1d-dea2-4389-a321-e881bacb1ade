package com.wacai.loan.goblin.query.dao.po;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description:实时监控查询
 * <AUTHOR>
 * @date 2022-03-07 17:51:05
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QueryAgentCollectionMonitor extends Query {

	/**
	 * 催收员列表
	 */
	private List<Long> agentIds;

	/**
	 * 队列列表
	 */
	List<String> assignQueueList;
	
	/**
	 * 租户
	 */
	private String tenant;
	
}
