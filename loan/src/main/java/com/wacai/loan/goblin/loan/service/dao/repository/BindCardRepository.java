package com.wacai.loan.goblin.loan.service.dao.repository;

import com.wacai.loan.goblin.loan.service.dao.po.BindCard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2020/5/14.
 */
public interface BindCardRepository extends JpaRepository<BindCard, Long> {

    BindCard findByLoanIdAndBankCardNo(Long loanId, String bankCardNo);

    BindCard findByLoanIdAndPriority(Long loanId, int priority);

    List<BindCard> findAllByLoanIdAndPriority(Long loanId, int priority);

    @Modifying
    @Transactional
    @Query(value = "UPDATE gbl_bind_card SET loan_id=?2 " +
            "WHERE loan_id=?1 ",
            nativeQuery = true)
    int updateLoanInfo(Long sourceLoanId, Long targetLoanId);

}
