package com.wacai.loan.goblin.loan.service.core;

import com.wacai.loan.goblin.loan.service.api.LoanIdContrastService;
import com.wacai.loan.goblin.loan.service.api.dto.LoanIdContrastDTO;
import com.wacai.loan.goblin.loan.service.core.util.LoanIdContrastConvert;
import com.wacai.loan.goblin.loan.service.dao.po.LoanIdContrast;
import com.wacai.loan.goblin.loan.service.dao.repository.LoanIdContrastRepository;
import com.wacai.loan.tenant.spring.bean.annotation.TenantConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by caoti on 2021/9/27.
 */
@Slf4j
@Service
@TenantConfig
public class DefaultLoanIdContrastService implements LoanIdContrastService {

    @Autowired
    private LoanIdContrastRepository loanIdContrastRepository;

    @Autowired
    private LoanIdContrastConvert loanIdContrastConvert;

    @Override
    public LoanIdContrastDTO findByOriginalLoanId(Long originalLoanId) {
        LoanIdContrast loanIdContrast = loanIdContrastRepository.findByOriginalLoanId(originalLoanId);
        return loanIdContrastConvert.toDTO(loanIdContrast);
    }

    @Override
    public LoanIdContrastDTO save(LoanIdContrastDTO loanIdContrastDTO) {
        if(loanIdContrastDTO == null) {
            return null;
        }
        LoanIdContrast loanIdContrast = loanIdContrastConvert.toPO(loanIdContrastDTO);
        LoanIdContrast save = loanIdContrastRepository.save(loanIdContrast);
        return loanIdContrastConvert.toDTO(save);
    }

    @Override
    public Long getMaxId(String batchNo) {
        return loanIdContrastRepository.getMaxId(batchNo);
    }

    @Override
    public List<LoanIdContrastDTO> findAllByMinIdAndBatchNoAndLimit(String batchNo, Long minId, int limit) {
        List<LoanIdContrast> loanIdContrastList = loanIdContrastRepository.findAllByMinIdAndBatchNoAndLimit(batchNo, minId, limit);
        return loanIdContrastConvert.toDTOList(loanIdContrastList);
    }

}
