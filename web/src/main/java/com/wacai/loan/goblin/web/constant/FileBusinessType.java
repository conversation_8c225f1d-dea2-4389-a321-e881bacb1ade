package com.wacai.loan.goblin.web.constant;

import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/9/14.
 */
public enum FileBusinessType {

    LEAVE_IT_TO_ME("LEAVE_IT_TO_ME","工作台_待我处理_导出"),
    DISTRIBUTION_OF_DOCUMENT("DISTRIBUTION_OF_DOCUMENT","单据管理_单据分配_导出"),
    IMPORT_LOAN_STATISTICS("IMPORT_LOAN_STATISTICS","报表_进件统计_导出"),
    STRATEGY_STATISTICS("STRATEGY_STATISTICS","报表_策略统计_明细下载"),
    SIMULATED_SUB_CASE("SIMULATED_SUB_CASE","分案策略_策略管理-编辑_下载结果"),
    RESULT_OF_EXECUTION("RESULT_OF_EXECUTION","分案策略_策略管理_执行结果_结果下载");

    @Getter
    private String value;

    @Getter
    private String desc;

    FileBusinessType(String value, String desc){
        this.value = value;
        this.desc = desc;
    }

    public static FileBusinessType getByVale(String value) {
        if (value == null) {
            return null;
        }
        for (FileBusinessType fileBusinessType : values()) {
            if (fileBusinessType.value.equals(value)) {
                return fileBusinessType;
            }
        }
        return null;
    }
}
