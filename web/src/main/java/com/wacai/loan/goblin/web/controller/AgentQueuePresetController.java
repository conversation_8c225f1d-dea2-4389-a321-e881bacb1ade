package com.wacai.loan.goblin.web.controller;

import com.wacai.loan.goblin.service.agent.api.AgentQueuePresetService;
import com.wacai.loan.goblin.service.agent.api.dto.AgentQueuePresetDTO;
import com.wacai.loan.goblin.tenant.api.dto.AuthType;
import com.wacai.loan.goblin.web.auth.annotation.Auth;
import com.wacai.loan.goblin.web.auth.impl.CommonAuth;
import com.wacai.loan.goblin.web.convert.impl.AgentQueuePresetConvert;
import com.wacai.loan.goblin.web.model.AgentQueuePresetVO;
import com.wacai.springboot.webapi.WebApiResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/1/14 2:36 PM
 */
@RestController
@RequestMapping("/agent-queue-preset")
@Slf4j
public class AgentQueuePresetController extends BaseController<AgentQueuePresetConvert, CommonAuth> {
    @Autowired
    private AgentQueuePresetService agentQueuePresetService;

    @RequestMapping(value = "/query", method = RequestMethod.GET)
    @ApiOperation(value = "催收员队列预设查询", httpMethod = "GET")
    @ResponseBody
    public WebApiResponse<AgentQueuePresetVO> agentQueuePreset(@RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM") Date date, @RequestParam("queue") String queue) {
        return WebApiResponse.success(convert.convertToModel(agentQueuePresetService.query(date, queue)));
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "催收员队列预设保存", httpMethod = "POST")
    @ResponseBody
    @Auth(allowLowestAuth = AuthType.WRITABLE)
    public WebApiResponse<String> presetAgentQueue(@Valid @RequestBody AgentQueuePresetVO agentQueuePresetVO) {
        agentQueuePresetService.save(convert.convertToDto(agentQueuePresetVO));
        return WebApiResponse.success("保存成功");
    }

}
