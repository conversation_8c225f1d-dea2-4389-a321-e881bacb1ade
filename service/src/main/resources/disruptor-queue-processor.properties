# Disruptor?????????
# ???application.properties?application.yml???

# ????Disruptor????????false????????
queue.processor.use.disruptor=true

# Disruptor??
# ???????????2?????????????????
queue.processor.buffer.size=65536

# ????????????CPU???
queue.processor.thread.num=8

# ??????????????????

# ??????
queue.processor.retry.max=3

# ??????
# ??????????
queue.processor.failure.log.enabled=true

# ????????
queue.processor.failure.log.path=/tmp/goblin/failed-assignments

# ????????
queue.processor.dead.letter.enabled=true

# Kafka??
kafka.queue.process.topic=loan.goblin.queue.process
kafka.consumer.group=com.wacai.loan.goblin
kafka.max.poll.records=500

# ???????
# 1. buffer.size: 
#    - ???????131072 (128K) ???
#    - ??????16384 (16K) ???
#    - ?????8192 (8K)
#
# 2. thread.num:
#    - CPU????CPU???
#    - IO????CPU??? * 2
#    - ????CPU??? * 1.5
#
# 3. batch.size:
#    - ?????1000-5000
#    - ????100-500
#    - ???500-1000
#
# 4. batch.timeout.ms:
#    - ??????50-100ms
#    - ?????200-500ms
#    - ???100-200ms

# ????
# ??????????????
queue.processor.monitor.enabled=true

# ???????DEBUG(??), INFO(??), WARN(???)
queue.processor.monitor.log.level=INFO

# ?????????
# - ??????5??????????????????
# - ??????10???????????????
# - ??????30??????????????????
#
# ??????????????????????????
# ?????????? QueueProcessMonitorService ?? @Scheduled ??

# ??JMX??
management.endpoints.jmx.exposure.include=*
management.endpoint.health.show-details=always

# ????
logging.level.com.wacai.loan.goblin.service.assign.consumer=INFO
logging.level.com.lmax.disruptor=WARN

# ?????????
# INFO?? - ???????????
# [2024-12-19 14:30:00] DisruptorConsumer - Processed: +1250 (4.2/s), Failed: +5, Success Rate: 99.60%, Buffer: 15.3%
#
# DEBUG?? - ?????????
# [2024-12-19 14:30:00] DisruptorConsumer Performance - Published: 12500 (+1250, 4.2/s), Processed: 12450 (+1245, 4.1/s), Failed: 50 (+5, 0.02/s), Success Rate: 99.60%, Buffer Usage: 15.3%
#
# ???????
# WARN [2024-12-19 14:30:00] WARNING ALERT - High failure rate: 12.50% (Published: 1000, Failed: 125)
# ERROR [2024-12-19 14:30:00] CRITICAL ALERT - Very high failure rate: 25.00% (Published: 1000, Failed: 250)
