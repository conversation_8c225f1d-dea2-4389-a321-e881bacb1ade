package com.wacai.loan.goblin.service.collection.dao.repository;

import com.alibaba.fastjson.JSONObject;
import com.wacai.common.middleware.util.StringUtils;
import com.wacai.loan.goblin.service.collection.dao.po.Assignment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by huisheng on 2017/6/16.
 */
@Slf4j
public class AssignmentCustomizeRepositoryImpl {

    @Autowired
    private EntityManager entityManager;

    public List<Assignment> getByCustomizeParamsAndRangeId(JSONObject params, Long startId, Long endId) {
        String sql = buildQuerySql(params);
        javax.persistence.Query query = entityManager.createNativeQuery(sql, Assignment.class);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        fillWhereParameter(query, params);
        return (List<Assignment>) query.getResultList();
    }

    public Long minIdByCustomizeParams(JSONObject params) {
        String sql = buildMinIdSql(params);
        javax.persistence.Query query = entityManager.createNativeQuery(sql);
        fillWhereParameter(query, params);
        Object value = query.getSingleResult();
        return toLongValue(value);
    }

    private Long toLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        if (value instanceof BigInteger) {
            return ((BigInteger) value).longValue();
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        return null;
    }

    private String buildMinIdSql(JSONObject params) {
        StringBuilder sql = new StringBuilder();
        sql.append("select min(id) ")
                .append("from gbl_assignment ")
                .append("WHERE active = 1 and status = 'ACTIVE' ");
        fillWhereConditionSql(sql, params);
        return sql.toString();
    }

    public Long maxIdByCustomizeParams(JSONObject params) {
        String sql = buildMaxIdSql(params);
        javax.persistence.Query query = entityManager.createNativeQuery(sql);
        fillWhereParameter(query, params);
        Object value = query.getSingleResult();
        return toLongValue(value);
    }

    private String buildMaxIdSql(JSONObject params) {
        StringBuilder sql = new StringBuilder();
        sql.append("select max(id) ")
                .append("from gbl_assignment ")
                .append("WHERE active = 1 and status = 'ACTIVE' ");
        fillWhereConditionSql(sql, params);
        return sql.toString();
    }


    private void fillWhereParameter(Query query, JSONObject params) {
        try {

            String startUpdatedTime = params.getString("startUpdatedTime");
            query.setParameter("startUpdatedTime", DateUtils.parseDate(startUpdatedTime, "yyyy-MM-dd HH:mm:ss"));
            String endAssignTime = params.getString("endUpdatedTime");
            query.setParameter("endUpdatedTime", DateUtils.parseDate(endAssignTime, "yyyy-MM-dd HH:mm:ss"));
            String agentIds = params.getString("agentIds");
            if (StringUtils.isNotBlank(agentIds)) {
                List<Long> agentIdList = strToLongList(agentIds);
                query.setParameter("agentIds", agentIdList);
            }
            String queues = params.getString("queues");
            if (StringUtils.isNotBlank(queues)) {
                List<String> queueList = strToStrList(queues);
                query.setParameter("queues", queueList);
            }
            String collectionIds = params.getString("collectionIds");
            if (StringUtils.isNotBlank(collectionIds)) {
                List<Long> collectionIdList = strToLongList(collectionIds);
                query.setParameter("collectionIds", collectionIdList);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private List<Long> strToLongList(String value) {
        String[] valueArray = value.split(",");
        List<Long> list = new ArrayList<>();
        for (String v : valueArray) {
            list.add(Long.valueOf(v));
        }
        return list;
    }

    private List<String> strToStrList(String value) {
        String[] valueArray = value.split(",");
        return Arrays.asList(valueArray);
    }

    private String buildQuerySql(JSONObject params) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * ");
        sql.append("FROM gbl_assignment ");
        sql.append("WHERE active = 1 and id >= :startId and id <= :endId and status = 'ACTIVE' ");
        fillWhereConditionSql(sql, params);
        return sql.toString();
    }

    private void fillWhereConditionSql(StringBuilder sql, JSONObject params) {
        sql.append(" and updated_time >= :startUpdatedTime");
        sql.append(" and updated_time <= :endUpdatedTime");
        String agentIds = params.getString("agentIds");
        if (StringUtils.isNotBlank(agentIds)) {
            sql.append(" and agent_id in (:agentIds)");
        }
        String queues = params.getString("queues");
        if (StringUtils.isNotBlank(queues)) {
            sql.append(" and queue in (:queues)");
        }
        String collectionIds = params.getString("collectionIds");
        if (StringUtils.isNotBlank(collectionIds)) {
            sql.append(" and collection_id in (:collectionIds)");
        }
    }
}
