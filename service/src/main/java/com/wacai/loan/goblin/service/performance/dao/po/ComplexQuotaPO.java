package com.wacai.loan.goblin.service.performance.dao.po;

import com.wacai.loan.goblin.service.common.po.BasePO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * @author: qiushui
 * @date: 2019-03-18 17:17
 */
@Data
@Entity
@Table(name = "gbl_complex_quota")
@EqualsAndHashCode(callSuper = false)
public class ComplexQuotaPO extends BasePO{

    private String name;

    private String detail;
}
