package com.wacai.loan.goblin.service.collection.dao.po;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.wacai.loan.goblin.service.common.po.BasePO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 催记外部动作关联表
 * <AUTHOR>
 * @date 2020-07-31 12:01:20
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "gbl_interaction_external_action")
public class InteractionExtAction extends BasePO {

	/**
	 * 催收日志id 待我处理查询需要join interactionId
	 */
	private Long interactionId;

	/**
	 * 第三方记录id
	 */
	private Long thirdPartyId;

	/**
	 * 第三方记录类型
	 */
	private String thirdPartyType;
	
	/**
	 * 第三方记录类型分组
	 */
	private String thirdPartyTypeGroup;

}
