package com.wacai.loan.goblin.service.collection.dao.po;

import com.wacai.loan.goblin.service.common.po.BasePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/20.
 */
@Data
@Table(name = "gbl_outstanding_assignment_preview")
@Entity
public class OutstandingAssignmentPreview extends BasePO{

    /**
     * 机构id
     */
    private Long agencyId;

    private Long agentId;

    /**
     * 案件id
     */
    private Long collectionId;

    /**
     * 预计出催日前
     */
    private Date expectExpireDate;

    /**
     * 机构预计出催日前
     */
    private Date agencyExpectExpireDate;

    /**
     * 分配时间
     */
    private Date assignTime;

    /**
     * 实际到期时间
     */
    private Date actualExpireDate;

    /**
     * 处理状态
     */
    private String disposeStatus;

    /**
     * 批次号
     */
    private Long batchNo;

    private String outstandingStatus;

    private String queue;

    private String assignArithmeticCode;

    private String channel;
}
