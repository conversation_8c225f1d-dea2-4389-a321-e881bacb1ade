package com.wacai.loan.goblin.service.task.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class AssetTagBean {
    /**
    * 主键
    */
    private Long id;
    /**
    * assetId
    */
    private String assetId;
    /**
     * 原订单号
     */
    private String orderNo;
    /**
    * 一级标签
    */
    private String parentTagCode;
    /**
    * 二级标签
    */
    private String tagCode;

    private Date createdTime;
    private Date updatedTime;
    private String createdBy;
    private String updatedBy;

}