package com.wacai.loan.goblin.service.assign.consumer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 失败处理服务
 * 
 * 负责处理队列处理失败的消息，包括：
 * 1. 记录失败日志
 * 2. 死信队列处理
 * 3. 失败重试机制
 * 4. 失败告警
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
public class FailureHandlingService {

    @Value("${queue.processor.failure.log.path:/tmp/goblin/failed-assignments}")
    private String failureLogPath;

    @Value("${queue.processor.failure.log.enabled:true}")
    private boolean failureLogEnabled;

    @Value("${queue.processor.dead.letter.enabled:true}")
    private boolean deadLetterEnabled;

    private final Executor asyncExecutor = Executors.newFixedThreadPool(2, r -> {
        Thread thread = new Thread(r, "FailureHandler-" + System.currentTimeMillis());
        thread.setDaemon(true);
        return thread;
    });

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 处理批处理失败
     */
    public void handleBatchFailure(List<AssignmentDTO> failedBatch, Exception exception, String handlerId) {
        if (failedBatch == null || failedBatch.isEmpty()) {
            return;
        }

        log.warn("Handling batch failure for handler {}: {} items failed", handlerId, failedBatch.size());

        // 异步处理失败记录，避免阻塞主流程
        CompletableFuture.runAsync(() -> {
            try {
                // 记录失败日志
                if (failureLogEnabled) {
                    recordFailureLog(failedBatch, exception, handlerId);
                }

                // 处理死信队列
                if (deadLetterEnabled) {
                    handleDeadLetterQueue(failedBatch, exception, handlerId);
                }

                // 发送告警（可以集成企业微信、钉钉等）
                sendFailureAlert(failedBatch, exception, handlerId);

            } catch (Exception e) {
                log.error("Error in failure handling for handler {}", handlerId, e);
            }
        }, asyncExecutor);
    }

    /**
     * 处理单个消息失败
     */
    public void handleSingleFailure(AssignmentDTO assignment, Exception exception, String handlerId, int retryCount) {
        log.warn("Handling single failure for handler {}: collectionId={}, retryCount={}", 
                handlerId, assignment.getCollectionId(), retryCount);

        CompletableFuture.runAsync(() -> {
            try {
                // 记录单个失败
                if (failureLogEnabled) {
                    recordSingleFailureLog(assignment, exception, handlerId, retryCount);
                }

                // 如果重试次数过多，加入死信队列
                if (retryCount >= 3 && deadLetterEnabled) {
                    handleDeadLetterQueue(Lists.newArrayList(assignment), exception, handlerId);
                }

            } catch (Exception e) {
                log.error("Error in single failure handling for handler {}", handlerId, e);
            }
        }, asyncExecutor);
    }

    /**
     * 记录批处理失败日志
     */
    private void recordFailureLog(List<AssignmentDTO> failedBatch, Exception exception, String handlerId) {
        try {
            Path logDir = Paths.get(failureLogPath);
            if (!Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }

            String fileName = String.format("batch-failures-%s.log", 
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            Path logFile = logDir.resolve(fileName);

            try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile.toFile(), true))) {
                String timestamp = LocalDateTime.now().format(dateTimeFormatter);
                writer.write(String.format("[%s] Handler: %s, BatchSize: %d, Exception: %s\n", 
                        timestamp, handlerId, failedBatch.size(), exception.getMessage()));

                for (AssignmentDTO dto : failedBatch) {
                    writer.write(String.format("  - CollectionId: %d, Queue: %s, StrategyId: %d, Data: %s\n",
                            dto.getCollectionId(), dto.getQueue(), dto.getStrategyId(), JSON.toJSONString(dto)));
                }
                writer.write("---\n");
                writer.flush();
            }

            log.info("Recorded batch failure log: {} items to {}", failedBatch.size(), logFile);

        } catch (IOException e) {
            log.error("Failed to record batch failure log for handler {}", handlerId, e);
        }
    }

    /**
     * 记录单个失败日志
     */
    private void recordSingleFailureLog(AssignmentDTO assignment, Exception exception, String handlerId, int retryCount) {
        try {
            Path logDir = Paths.get(failureLogPath);
            if (!Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }

            String fileName = String.format("single-failures-%s.log", 
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            Path logFile = logDir.resolve(fileName);

            try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile.toFile(), true))) {
                String timestamp = LocalDateTime.now().format(dateTimeFormatter);
                writer.write(String.format("[%s] Handler: %s, RetryCount: %d, Exception: %s\n", 
                        timestamp, handlerId, retryCount, exception.getMessage()));
                writer.write(String.format("  CollectionId: %d, Queue: %s, StrategyId: %d, Data: %s\n",
                        assignment.getCollectionId(), assignment.getQueue(), assignment.getStrategyId(), 
                        JSON.toJSONString(assignment)));
                writer.write("---\n");
                writer.flush();
            }

        } catch (IOException e) {
            log.error("Failed to record single failure log for handler {}", handlerId, e);
        }
    }

    /**
     * 处理死信队列
     */
    private void handleDeadLetterQueue(List<AssignmentDTO> failedItems, Exception exception, String handlerId) {
        // 这里可以实现将失败的消息发送到死信队列
        // 例如发送到Kafka的死信topic，或者存储到数据库中
        
        log.warn("Adding {} items to dead letter queue from handler {}", failedItems.size(), handlerId);
        
        for (AssignmentDTO dto : failedItems) {
            // 示例：可以发送到专门的死信topic
            // kafkaTemplate.send("dead-letter-topic", JSON.toJSONString(dto));
            
            log.warn("Dead letter item: collectionId={}, queue={}, strategyId={}", 
                    dto.getCollectionId(), dto.getQueue(), dto.getStrategyId());
        }
    }

    /**
     * 发送失败告警
     */
    private void sendFailureAlert(List<AssignmentDTO> failedBatch, Exception exception, String handlerId) {
        // 这里可以集成企业微信、钉钉、邮件等告警系统
        
        if (failedBatch.size() > 100) { // 只有批量失败较多时才发送告警
            log.error("ALERT: Large batch failure detected! Handler: {}, Failed items: {}, Exception: {}", 
                    handlerId, failedBatch.size(), exception.getMessage());
            
            // 示例告警逻辑
            // alertService.sendAlert("Queue Process Failure", 
            //     String.format("Handler %s failed to process %d items", handlerId, failedBatch.size()));
        }
    }

    /**
     * 获取失败处理统计信息
     */
    public FailureStatistics getFailureStatistics() {
        // 这里可以实现统计信息的收集
        // 例如从日志文件中统计失败次数、失败类型等
        
        FailureStatistics stats = new FailureStatistics();
        stats.setFailureLogEnabled(failureLogEnabled);
        stats.setDeadLetterEnabled(deadLetterEnabled);
        stats.setFailureLogPath(failureLogPath);
        
        return stats;
    }

    /**
     * 失败统计信息
     */
    public static class FailureStatistics {
        private boolean failureLogEnabled;
        private boolean deadLetterEnabled;
        private String failureLogPath;
        private long totalFailures;
        private long deadLetterCount;

        // Getters and Setters
        public boolean isFailureLogEnabled() { return failureLogEnabled; }
        public void setFailureLogEnabled(boolean failureLogEnabled) { this.failureLogEnabled = failureLogEnabled; }
        public boolean isDeadLetterEnabled() { return deadLetterEnabled; }
        public void setDeadLetterEnabled(boolean deadLetterEnabled) { this.deadLetterEnabled = deadLetterEnabled; }
        public String getFailureLogPath() { return failureLogPath; }
        public void setFailureLogPath(String failureLogPath) { this.failureLogPath = failureLogPath; }
        public long getTotalFailures() { return totalFailures; }
        public void setTotalFailures(long totalFailures) { this.totalFailures = totalFailures; }
        public long getDeadLetterCount() { return deadLetterCount; }
        public void setDeadLetterCount(long deadLetterCount) { this.deadLetterCount = deadLetterCount; }
    }
}
