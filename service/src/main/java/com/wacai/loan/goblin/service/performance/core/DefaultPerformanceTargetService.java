package com.wacai.loan.goblin.service.performance.core;

import com.wacai.loan.goblin.common.util.JacksonUtil;
import com.wacai.loan.goblin.service.performance.api.PerformanceTargetService;
import com.wacai.loan.goblin.service.performance.api.dto.PerformanceTargetDTO;
import com.wacai.loan.goblin.service.performance.dao.po.PerformanceTargetPO;
import com.wacai.loan.goblin.service.performance.dao.repository.PerformanceTargetRepository;

import com.wacai.loan.tenant.spring.bean.annotation.TenantConfig;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: qiushui
 * @date: 2019-03-13 17:33
 */
@Slf4j
@Service
@TenantConfig
public class DefaultPerformanceTargetService implements PerformanceTargetService {

    @Autowired
    private PerformanceTargetRepository performanceTargetRepository;

    @Override
    public void save(PerformanceTargetDTO performanceTargetDTO) {
        if(performanceTargetDTO != null){
            performanceTargetRepository.save(convert(performanceTargetDTO));
        }
    }

    @Override
    public void delete(PerformanceTargetDTO performanceTargetDTO) {
        if(performanceTargetDTO != null && performanceTargetDTO.getId() != null){
            performanceTargetRepository.deleteById(performanceTargetDTO.getId());
        }
    }

    @Override
    public List<PerformanceTargetDTO> findByQueueAndMonth(String queue, String month) {
        List<PerformanceTargetPO> performanceTargetPOList = performanceTargetRepository.findByQueueAndMonth(queue,month);
        if(CollectionUtils.isEmpty(performanceTargetPOList)){
            return Collections.emptyList();
        }
        return performanceTargetPOList.stream().map(item -> {return convert(item);}).collect(Collectors.toList());
    }

    @Override
    public PerformanceTargetDTO findByQueueAndMonthAndAgentId(String queue, String month, Long agentId) {
        PerformanceTargetPO performanceTargetPO = performanceTargetRepository.findByQueueAndMonthAndAgentId(queue,month,agentId);
        if(performanceTargetPO == null){
            return null;
        }
        return convert(performanceTargetPO);
    }

    @Override
    public PerformanceTargetDTO findById(Long id) {
        PerformanceTargetPO performanceTargetPO = performanceTargetRepository.findById(id).orElse(null);
        if(performanceTargetPO == null){
            return null;
        }
        return convert(performanceTargetPO);
    }

    private PerformanceTargetDTO convert(PerformanceTargetPO performanceTargetPO){
        PerformanceTargetDTO performanceTargetDTO = new PerformanceTargetDTO();
        BeanUtils.copyProperties(performanceTargetPO,performanceTargetDTO);
        return performanceTargetDTO;
    }

    private PerformanceTargetPO convert(PerformanceTargetDTO performanceTargetDTO){
        PerformanceTargetPO performanceTargetPO = new PerformanceTargetPO();
        BeanUtils.copyProperties(performanceTargetDTO,performanceTargetPO);
        
        log.info("convert: performanceTargetPO = {}", JacksonUtil.objToJson(performanceTargetPO));
        
        return performanceTargetPO;
    }

}
