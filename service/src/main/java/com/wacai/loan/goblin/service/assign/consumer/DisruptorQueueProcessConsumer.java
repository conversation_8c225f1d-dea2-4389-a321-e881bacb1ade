package com.wacai.loan.goblin.service.assign.consumer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumer;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import com.wacai.gbl.kafka.agent.consumer.Message;
import com.wacai.loan.goblin.service.assign.api.QueueProcessorMap;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于Disruptor的高性能队列处理消费者
 * 
 * 优势：
 * 1. 高性能：使用无锁环形缓冲区，减少GC压力
 * 2. 数据安全：支持批处理和失败重试机制
 * 3. 可监控：提供详细的性能指标和状态监控
 * 4. 易维护：代码结构清晰，职责分离
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
@Slf4j
public class DisruptorQueueProcessConsumer extends KafkaConsumer implements InitializingBean, DisposableBean {

    @Autowired
    private QueueProcessorMap queueProcessorMap;

    @Autowired
    private FailureHandlingService failureHandlingService;

    @Value("${queue.processor.thread.num:8}")
    private int consumerThreadNum;

    @Value("${queue.processor.buffer.size:65536}")
    private int bufferSize;

    @Value("${queue.processor.batch.size:1000}")
    private int batchSize;

    @Value("${queue.processor.batch.timeout.ms:100}")
    private long batchTimeoutMs;

    @Value("${queue.processor.retry.max:3}")
    private int maxRetryCount;

    private Disruptor<AssignmentEvent> disruptor;
    private RingBuffer<AssignmentEvent> ringBuffer;
    private BatchAssignmentEventHandler[] handlers;
    private final AtomicLong publishedCount = new AtomicLong(0);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong failedCount = new AtomicLong(0);

    public DisruptorQueueProcessConsumer(KafkaConsumerConfig consumerConfig) {
        super(consumerConfig);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initDisruptor();
        log.info("DisruptorQueueProcessConsumer initialized with bufferSize={}, batchSize={}, consumerThreadNum={}", 
                bufferSize, batchSize, consumerThreadNum);
    }

    @Override
    public void destroy() throws Exception {
        if (disruptor != null) {
            log.info("Shutting down DisruptorQueueProcessConsumer...");

            // 先关闭处理器，确保处理完剩余的批次
            if (handlers != null) {
                for (BatchAssignmentEventHandler handler : handlers) {
                    if (handler != null) {
                        handler.shutdown();
                    }
                }
            }

            // 然后关闭Disruptor
            disruptor.shutdown();
            log.info("DisruptorQueueProcessConsumer shutdown completed. Published: {}, Processed: {}, Failed: {}",
                    publishedCount.get(), processedCount.get(), failedCount.get());
        }
    }

    @Override
    public void onMessageReceived(Message message) {
        String messageJson = new String(message.getValue(), StandardCharsets.UTF_8);
        try {
            AssignmentDTO assignmentDTO = JSONObject.parseObject(messageJson, AssignmentDTO.class);

            // 检查消息是否为空或无效
            if (assignmentDTO == null || assignmentDTO.getCollectionId() == null) {
                log.warn("Received invalid message, skipping: {}", messageJson);
                return;
            }

            // 发布事件到Disruptor - 使用tryNext避免阻塞
            long sequence = -1;
            try {
                sequence = ringBuffer.tryNext();
            } catch (InsufficientCapacityException e) {
                // 缓冲区满，记录告警但不丢失消息
                log.warn("Disruptor buffer is full, using blocking next() - consider increasing buffer size");
                sequence = ringBuffer.next(); // 阻塞等待
            }

            try {
                AssignmentEvent event = ringBuffer.get(sequence);
                event.setAssignmentDTO(assignmentDTO);
                event.setMessageOffset(message.getOffset());
                event.setRetryCount(0);
                event.setCreateTime(System.currentTimeMillis());

                publishedCount.incrementAndGet();

                log.debug("Published message to disruptor: offset={}, queue={}, collectionId={}",
                        message.getOffset(), assignmentDTO.getQueue(), assignmentDTO.getCollectionId());
            } finally {
                ringBuffer.publish(sequence);
            }
        } catch (Exception e) {
            log.error("Failed to publish message to disruptor: {}", messageJson, e);
            failedCount.incrementAndGet();

            // 重要：发布失败的消息也需要处理，避免数据丢失
            // 可以考虑直接调用失败处理服务或写入文件
            handlePublishFailure(messageJson, e);
        }
    }

    /**
     * 处理发布到Disruptor失败的消息
     */
    private void handlePublishFailure(String messageJson, Exception e) {
        try {
            AssignmentDTO dto = JSONObject.parseObject(messageJson, AssignmentDTO.class);
            if (dto != null) {
                failureHandlingService.handleSingleFailure(dto, e, "Publisher", 0);
            }
        } catch (Exception parseException) {
            log.error("Failed to parse message for failure handling: {}", messageJson, parseException);
            // 最后的保障：写入文件
            writeToFailureFile(messageJson, e);
        }
    }

    /**
     * 最后的保障：将无法处理的消息写入文件
     */
    private void writeToFailureFile(String messageJson, Exception e) {
        // 这里可以实现写入文件的逻辑，确保消息不丢失
        log.error("Writing unparseable message to failure file: {}", messageJson, e);
    }

    private void initDisruptor() {
        // 确保bufferSize是2的幂
        int actualBufferSize = Integer.highestOneBit(bufferSize) == bufferSize ? bufferSize : Integer.highestOneBit(bufferSize) << 1;
        
        // 创建事件工厂
        EventFactory<AssignmentEvent> eventFactory = AssignmentEvent::new;
        
        // 创建自定义线程工厂
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger(0);
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "DisruptorQueueProcessor-" + counter.incrementAndGet());
                thread.setDaemon(false);
                return thread;
            }
        };

        // 创建Disruptor
        disruptor = new Disruptor<>(
                eventFactory,
                actualBufferSize,
                threadFactory,
                ProducerType.SINGLE,  // 单生产者模式，性能更好
                new BlockingWaitStrategy()  // 阻塞等待策略，CPU友好
        );

        // 设置异常处理器
        disruptor.setDefaultExceptionHandler(new DisruptorExceptionHandler());

        // 创建事件处理器数组
        handlers = new BatchAssignmentEventHandler[consumerThreadNum];
        for (int i = 0; i < consumerThreadNum; i++) {
            handlers[i] = new BatchAssignmentEventHandler(i);
        }

        // 使用WorkerPool实现多消费者模式
        disruptor.handleEventsWithWorkerPool(handlers);

        // 启动Disruptor
        ringBuffer = disruptor.start();
        
        log.info("Disruptor started with actualBufferSize={}", actualBufferSize);
    }

    /**
     * 分配事件数据结构
     */
    public static class AssignmentEvent {
        private AssignmentDTO assignmentDTO;
        private long messageOffset;
        private int retryCount;
        private long createTime;

        public AssignmentDTO getAssignmentDTO() { return assignmentDTO; }
        public void setAssignmentDTO(AssignmentDTO assignmentDTO) { this.assignmentDTO = assignmentDTO; }
        public long getMessageOffset() { return messageOffset; }
        public void setMessageOffset(long messageOffset) { this.messageOffset = messageOffset; }
        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public void clear() {
            this.assignmentDTO = null;
            this.messageOffset = 0;
            this.retryCount = 0;
            this.createTime = 0;
        }
    }

    /**
     * 事件包装类，用于在批处理中保存事件的完整信息
     */
    private static class AssignmentEventWrapper {
        private final AssignmentDTO assignmentDTO;
        private final int retryCount;
        private final long createTime;

        public AssignmentEventWrapper(AssignmentDTO assignmentDTO, int retryCount, long createTime) {
            this.assignmentDTO = assignmentDTO;
            this.retryCount = retryCount;
            this.createTime = createTime;
        }

        public AssignmentDTO getAssignmentDTO() { return assignmentDTO; }
        public int getRetryCount() { return retryCount; }
        public long getCreateTime() { return createTime; }
    }

    /**
     * Disruptor异常处理器
     */
    private class DisruptorExceptionHandler implements ExceptionHandler<AssignmentEvent> {
        @Override
        public void handleEventException(Throwable ex, long sequence, AssignmentEvent event) {
            log.error("Exception processing event at sequence {}: collectionId={}, queue={}", 
                    sequence, 
                    event.getAssignmentDTO() != null ? event.getAssignmentDTO().getCollectionId() : "null",
                    event.getAssignmentDTO() != null ? event.getAssignmentDTO().getQueue() : "null", 
                    ex);
            failedCount.incrementAndGet();
        }

        @Override
        public void handleOnStartException(Throwable ex) {
            log.error("Exception during disruptor start", ex);
        }

        @Override
        public void handleOnShutdownException(Throwable ex) {
            log.error("Exception during disruptor shutdown", ex);
        }
    }

    /**
     * 批处理事件处理器 - 使用WorkHandler实现负载均衡
     */
    private class BatchAssignmentEventHandler implements WorkHandler<AssignmentEvent> {
        private final int handlerId;
        private final List<AssignmentEventWrapper> batchBuffer = new ArrayList<>(batchSize);
        private long lastBatchTime = System.currentTimeMillis();
        private volatile boolean shutdown = false;

        public BatchAssignmentEventHandler(int handlerId) {
            this.handlerId = handlerId;
            // 启动定时批处理线程
            startBatchTimeoutChecker();
        }

        @Override
        public void onEvent(AssignmentEvent event) throws Exception {
            if (shutdown) {
                return;
            }

            if (event.getAssignmentDTO() != null) {
                synchronized (batchBuffer) {
                    // 使用包装类保存完整的事件信息
                    AssignmentEventWrapper wrapper = new AssignmentEventWrapper(
                            event.getAssignmentDTO(),
                            event.getRetryCount(),
                            event.getCreateTime()
                    );
                    batchBuffer.add(wrapper);

                    // 检查是否需要立即处理
                    if (batchBuffer.size() >= batchSize) {
                        processBatch();
                    }
                }
            }

            // 清理事件对象以便重用
            event.clear();
        }

        /**
         * 启动定时检查器，处理超时的批次
         */
        private void startBatchTimeoutChecker() {
            Thread timeoutChecker = new Thread(() -> {
                while (!shutdown && !Thread.currentThread().isInterrupted()) {
                    try {
                        Thread.sleep(batchTimeoutMs);

                        synchronized (batchBuffer) {
                            if (!batchBuffer.isEmpty() &&
                                (System.currentTimeMillis() - lastBatchTime) >= batchTimeoutMs) {
                                processBatch();
                            }
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        log.error("Error in batch timeout checker for handler-{}", handlerId, e);
                    }
                }
            }, "BatchTimeoutChecker-" + handlerId);

            timeoutChecker.setDaemon(true);
            timeoutChecker.start();
        }

        public void shutdown() {
            shutdown = true;
            // 处理剩余的批次
            synchronized (batchBuffer) {
                if (!batchBuffer.isEmpty()) {
                    processBatch();
                }
            }
        }

        private void processBatch() {
            if (batchBuffer.isEmpty()) {
                return;
            }

            String batchId = UUID.randomUUID().toString().substring(0, 8);
            Stopwatch stopwatch = Stopwatch.createStarted();
            int batchSizeLocal = batchBuffer.size();

            log.debug("Handler-{} processing batch {} with {} items", handlerId, batchId, batchSizeLocal);

            try {
                // 提取AssignmentDTO列表进行批量处理
                List<AssignmentDTO> dtoList = new ArrayList<>(batchSizeLocal);
                for (AssignmentEventWrapper wrapper : batchBuffer) {
                    dtoList.add(wrapper.getAssignmentDTO());
                }

                // 批量处理
                queueProcessorMap.streamingProcessQueue(dtoList);
                processedCount.addAndGet(batchSizeLocal);

                log.info("Handler-{} batch {} processed successfully: {} items in {}ms",
                        handlerId, batchId, batchSizeLocal, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            } catch (Exception e) {
                log.error("Handler-{} batch {} processing failed: {} items", handlerId, batchId, batchSizeLocal, e);
                failedCount.addAndGet(batchSizeLocal);

                // 处理失败的批次，包含重试逻辑
                handleBatchFailure(new ArrayList<>(batchBuffer), e);
            } finally {
                batchBuffer.clear();
                lastBatchTime = System.currentTimeMillis();
            }
        }

        private void handleBatchFailure(List<AssignmentEventWrapper> failedBatch, Exception e) {
            // 提取DTO列表用于失败处理服务
            List<AssignmentDTO> dtoList = new ArrayList<>();
            for (AssignmentEventWrapper wrapper : failedBatch) {
                dtoList.add(wrapper.getAssignmentDTO());
            }

            // 使用专门的失败处理服务
            failureHandlingService.handleBatchFailure(dtoList, e, "Handler-" + handlerId);

            // 重要：将失败的消息重新发布到Disruptor进行重试
            // 但需要增加重试计数，避免无限重试
            for (AssignmentEventWrapper wrapper : failedBatch) {
                retryFailedMessage(wrapper, e);
            }
        }

        private void retryFailedMessage(AssignmentEventWrapper wrapper, Exception originalException) {
            try {
                AssignmentDTO dto = wrapper.getAssignmentDTO();
                int currentRetryCount = wrapper.getRetryCount();

                if (currentRetryCount < maxRetryCount) {
                    // 增加重试计数
                    int newRetryCount = currentRetryCount + 1;

                    // 重新发布到Disruptor
                    long sequence = ringBuffer.next();
                    try {
                        AssignmentEvent event = ringBuffer.get(sequence);
                        event.setAssignmentDTO(dto);
                        event.setMessageOffset(0); // 重试消息没有原始offset
                        event.setRetryCount(newRetryCount);
                        event.setCreateTime(System.currentTimeMillis());

                        log.warn("Retrying failed message: collectionId={}, queue={}, retryCount={}",
                                dto.getCollectionId(), dto.getQueue(), newRetryCount);
                    } finally {
                        ringBuffer.publish(sequence);
                    }
                } else {
                    // 超过最大重试次数，发送到死信队列
                    log.error("Message exceeded max retry count, sending to dead letter queue: collectionId={}, queue={}",
                            dto.getCollectionId(), dto.getQueue());
                    failureHandlingService.handleSingleFailure(dto, originalException, "Handler-" + handlerId, currentRetryCount);
                }
            } catch (Exception retryException) {
                log.error("Failed to retry message: collectionId={}, queue={}",
                        wrapper.getAssignmentDTO().getCollectionId(), wrapper.getAssignmentDTO().getQueue(), retryException);
                // 重试失败也要记录到死信队列
                failureHandlingService.handleSingleFailure(wrapper.getAssignmentDTO(), retryException, "Handler-" + handlerId, wrapper.getRetryCount());
            }
        }
    }

    /**
     * 获取性能统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("publishedCount", publishedCount.get());
        stats.put("processedCount", processedCount.get());
        stats.put("failedCount", failedCount.get());
        stats.put("bufferSize", bufferSize);
        stats.put("consumerThreadNum", consumerThreadNum);
        stats.put("batchSize", batchSize);
        
        if (ringBuffer != null) {
            stats.put("remainingCapacity", ringBuffer.remainingCapacity());
            stats.put("bufferUtilization", (double)(bufferSize - ringBuffer.remainingCapacity()) / bufferSize);
        }
        
        return stats;
    }
}
