package com.wacai.loan.goblin.service.assign.consumer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumer;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import com.wacai.gbl.kafka.agent.consumer.Message;
import com.wacai.loan.goblin.service.assign.api.QueueProcessorMap;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于Disruptor的高性能队列处理消费者
 * 
 * 优势：
 * 1. 高性能：使用无锁环形缓冲区，减少GC压力
 * 2. 数据安全：支持批处理和失败重试机制
 * 3. 可监控：提供详细的性能指标和状态监控
 * 4. 易维护：代码结构清晰，职责分离
 * 
 * <AUTHOR>
 * @date 2025/07/07
 */
@Slf4j
public class DisruptorQueueProcessConsumer extends KafkaConsumer implements InitializingBean, DisposableBean {

    @Autowired
    private QueueProcessorMap queueProcessorMap;

    @Autowired
    private FailureHandlingService failureHandlingService;

    @Value("${queue.processor.thread.num:8}")
    private int consumerThreadNum;

    @Value("${queue.processor.buffer.size:65536}")
    private int bufferSize;

    @Value("${queue.processor.batch.size:1000}")
    private int batchSize;

    @Value("${queue.processor.batch.timeout.ms:100}")
    private long batchTimeoutMs;

    @Value("${queue.processor.retry.max:3}")
    private int maxRetryCount;

    private Disruptor<AssignmentEvent> disruptor;
    private RingBuffer<AssignmentEvent> ringBuffer;
    private final AtomicLong publishedCount = new AtomicLong(0);
    private final AtomicLong processedCount = new AtomicLong(0);
    private final AtomicLong failedCount = new AtomicLong(0);

    public DisruptorQueueProcessConsumer(KafkaConsumerConfig consumerConfig) {
        super(consumerConfig);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initDisruptor();
        log.info("DisruptorQueueProcessConsumer initialized with bufferSize={}, batchSize={}, consumerThreadNum={}", 
                bufferSize, batchSize, consumerThreadNum);
    }

    @Override
    public void destroy() throws Exception {
        if (disruptor != null) {
            log.info("Shutting down DisruptorQueueProcessConsumer...");
            disruptor.shutdown();
            log.info("DisruptorQueueProcessConsumer shutdown completed. Published: {}, Processed: {}, Failed: {}", 
                    publishedCount.get(), processedCount.get(), failedCount.get());
        }
    }

    @Override
    public void onMessageReceived(Message message) {
        String messageJson = new String(message.getValue(), StandardCharsets.UTF_8);
        try {
            AssignmentDTO assignmentDTO = JSONObject.parseObject(messageJson, AssignmentDTO.class);
            
            // 发布事件到Disruptor
            long sequence = ringBuffer.next();
            try {
                AssignmentEvent event = ringBuffer.get(sequence);
                event.setAssignmentDTO(assignmentDTO);
                event.setMessageOffset(message.getOffset());
                event.setRetryCount(0);
                event.setCreateTime(System.currentTimeMillis());
                
                publishedCount.incrementAndGet();
                
                log.debug("Published message to disruptor: offset={}, queue={}, collectionId={}", 
                        message.getOffset(), assignmentDTO.getQueue(), assignmentDTO.getCollectionId());
            } finally {
                ringBuffer.publish(sequence);
            }
        } catch (Exception e) {
            log.error("Failed to publish message to disruptor: {}", messageJson, e);
            failedCount.incrementAndGet();
        }
    }

    private void initDisruptor() {
        // 确保bufferSize是2的幂
        int actualBufferSize = Integer.highestOneBit(bufferSize) == bufferSize ? bufferSize : Integer.highestOneBit(bufferSize) << 1;
        
        // 创建事件工厂
        EventFactory<AssignmentEvent> eventFactory = AssignmentEvent::new;
        
        // 创建自定义线程工厂
        ThreadFactory threadFactory = new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger(0);
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r, "DisruptorQueueProcessor-" + counter.incrementAndGet());
                thread.setDaemon(false);
                return thread;
            }
        };

        // 创建Disruptor
        disruptor = new Disruptor<>(
                eventFactory,
                actualBufferSize,
                threadFactory,
                ProducerType.SINGLE,  // 单生产者模式，性能更好
                new BlockingWaitStrategy()  // 阻塞等待策略，CPU友好
        );

        // 设置异常处理器
        disruptor.setDefaultExceptionHandler(new DisruptorExceptionHandler());

        // 创建批处理事件处理器
        BatchEventProcessor<AssignmentEvent>[] processors = new BatchEventProcessor[consumerThreadNum];
        for (int i = 0; i < consumerThreadNum; i++) {
            BatchAssignmentEventHandler handler = new BatchAssignmentEventHandler(i);
            processors[i] = new BatchEventProcessor<>(disruptor.getRingBuffer(), 
                    disruptor.getRingBuffer().newBarrier(), handler);
            disruptor.getRingBuffer().addGatingSequences(processors[i].getSequence());
        }

        // 启动处理器
        for (BatchEventProcessor<AssignmentEvent> processor : processors) {
            new Thread(processor, "BatchProcessor-" + processor.toString()).start();
        }

        // 启动Disruptor
        ringBuffer = disruptor.start();
        
        log.info("Disruptor started with actualBufferSize={}", actualBufferSize);
    }

    /**
     * 分配事件数据结构
     */
    public static class AssignmentEvent {
        private AssignmentDTO assignmentDTO;
        private long messageOffset;
        private int retryCount;
        private long createTime;

        public AssignmentDTO getAssignmentDTO() { return assignmentDTO; }
        public void setAssignmentDTO(AssignmentDTO assignmentDTO) { this.assignmentDTO = assignmentDTO; }
        public long getMessageOffset() { return messageOffset; }
        public void setMessageOffset(long messageOffset) { this.messageOffset = messageOffset; }
        public int getRetryCount() { return retryCount; }
        public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
        public long getCreateTime() { return createTime; }
        public void setCreateTime(long createTime) { this.createTime = createTime; }

        public void clear() {
            this.assignmentDTO = null;
            this.messageOffset = 0;
            this.retryCount = 0;
            this.createTime = 0;
        }
    }

    /**
     * Disruptor异常处理器
     */
    private class DisruptorExceptionHandler implements ExceptionHandler<AssignmentEvent> {
        @Override
        public void handleEventException(Throwable ex, long sequence, AssignmentEvent event) {
            log.error("Exception processing event at sequence {}: collectionId={}, queue={}", 
                    sequence, 
                    event.getAssignmentDTO() != null ? event.getAssignmentDTO().getCollectionId() : "null",
                    event.getAssignmentDTO() != null ? event.getAssignmentDTO().getQueue() : "null", 
                    ex);
            failedCount.incrementAndGet();
        }

        @Override
        public void handleOnStartException(Throwable ex) {
            log.error("Exception during disruptor start", ex);
        }

        @Override
        public void handleOnShutdownException(Throwable ex) {
            log.error("Exception during disruptor shutdown", ex);
        }
    }

    /**
     * 批处理事件处理器
     */
    private class BatchAssignmentEventHandler implements EventHandler<AssignmentEvent> {
        private final int handlerId;
        private final List<AssignmentDTO> batchBuffer = new ArrayList<>(batchSize);
        private long lastBatchTime = System.currentTimeMillis();

        public BatchAssignmentEventHandler(int handlerId) {
            this.handlerId = handlerId;
        }

        @Override
        public void onEvent(AssignmentEvent event, long sequence, boolean endOfBatch) throws Exception {
            if (event.getAssignmentDTO() != null) {
                batchBuffer.add(event.getAssignmentDTO());
            }

            // 批处理触发条件：达到批大小 或 到达批次结束 或 超时
            boolean shouldProcess = batchBuffer.size() >= batchSize ||
                                  endOfBatch ||
                                  (System.currentTimeMillis() - lastBatchTime) >= batchTimeoutMs;

            if (shouldProcess && !batchBuffer.isEmpty()) {
                processBatch();
            }

            // 清理事件对象以便重用
            event.clear();
        }

        private void processBatch() {
            if (batchBuffer.isEmpty()) {
                return;
            }

            String batchId = UUID.randomUUID().toString().substring(0, 8);
            Stopwatch stopwatch = Stopwatch.createStarted();
            int batchSizeLocal = batchBuffer.size();

            log.debug("Handler-{} processing batch {} with {} items", handlerId, batchId, batchSizeLocal);

            try {
                // 批量处理
                queueProcessorMap.streamingProcessQueue(new ArrayList<>(batchBuffer));
                processedCount.addAndGet(batchSizeLocal);
                
                log.info("Handler-{} batch {} processed successfully: {} items in {}ms", 
                        handlerId, batchId, batchSizeLocal, stopwatch.elapsed(TimeUnit.MILLISECONDS));
            } catch (Exception e) {
                log.error("Handler-{} batch {} processing failed: {} items", handlerId, batchId, batchSizeLocal, e);
                failedCount.addAndGet(batchSizeLocal);
                
                // 这里可以添加失败重试逻辑或死信队列处理
                handleBatchFailure(batchBuffer, e);
            } finally {
                batchBuffer.clear();
                lastBatchTime = System.currentTimeMillis();
            }
        }

        private void handleBatchFailure(List<AssignmentDTO> failedBatch, Exception e) {
            // 使用专门的失败处理服务
            failureHandlingService.handleBatchFailure(failedBatch, e, "Handler-" + handlerId);
        }
    }

    /**
     * 获取性能统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("publishedCount", publishedCount.get());
        stats.put("processedCount", processedCount.get());
        stats.put("failedCount", failedCount.get());
        stats.put("bufferSize", bufferSize);
        stats.put("consumerThreadNum", consumerThreadNum);
        stats.put("batchSize", batchSize);
        
        if (ringBuffer != null) {
            stats.put("remainingCapacity", ringBuffer.remainingCapacity());
            stats.put("bufferUtilization", (double)(bufferSize - ringBuffer.remainingCapacity()) / bufferSize);
        }
        
        return stats;
    }
}
