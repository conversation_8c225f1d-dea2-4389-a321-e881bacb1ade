package com.wacai.loan.goblin.service.assign.core.support;

import com.wacai.common.redis.RedisCluster;
import com.wacai.common.redis.RedisException;
import com.wacai.common.redis.util.Tuple;
import com.wacai.loan.goblin.common.util.DateUtils;
import com.wacai.loan.goblin.service.assign.core.entity.AssignScore;
import com.wacai.loan.goblin.service.common.lock.LockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/7
 */
@Slf4j
public abstract class AssignAvgSupport {

    @Autowired
    private RedisCluster redisCluster;

    @Autowired
    private LockService lockService;

    private String ASSIGN_LOCK = "ASSIGN_LOCK";
    private final String CASE_COUNT_LOCK = "CASE_COUNT_LOCK";

    private String ASSIGN_INIT_LOCK = "ASSIGN_INIT_LOCK";
    private String ASSIGN_COLLECTION_LOCK = "ASSIGN_COLLECTION_LOCK";

    @Value("${assign.avg.init.lock.time:120}")
    private int initLockTime;

    @Value("${assign.avg.expire.time:600}")
    private int expireTime;


    protected abstract BigDecimal getInitScoreByAgentId(String month, String queue, Long agentId);

    protected abstract Long getAssignedCountByAgentId(String month, String queue, Long agentId);


    protected String getPreKey() {
        return this.getClass().getSimpleName();
    }

    protected abstract String buildKey(String month, String queue);

    public void checkAndInit(String month, String queue, List<Integer> agentIds) {
        String principleKey = getPrincipleKey(month, queue);
        String caseCountKey = getCaseCountKey(month, queue);
        String lockValue = queue + "_" + System.currentTimeMillis();

        Set<String> scoreAgentIds = getAllAgentId(principleKey);
        Set<String> caseCountAgentIds = getAllAgentId(caseCountKey);
        if (CollectionUtils.isEmpty(scoreAgentIds) || CollectionUtils.isEmpty(caseCountAgentIds)) {
            scoreAgentIds = new HashSet<>();
            caseCountAgentIds = new HashSet<>();
        }
        int agentCount = agentIds.size();
        if ((scoreAgentIds.size() == agentCount) && (caseCountAgentIds.size() == agentCount)) {
            //无变化 无需初始化
            return;
        }
        boolean lockResult = lockService.lock(ASSIGN_INIT_LOCK, principleKey, lockValue, initLockTime);
        if (!lockResult) {
            log.error("could not found initAssignAgent lock by {}", queue);
            return;
        }
        try {
            log.info("checkAndInit queue {} and agentIds {}", queue, agentIds);
            Map<String, Long> agentIdMap = convertToMapByAgentIds(agentIds);
            Map<String, Long> scoreAgentIdMap = convertToMapByScoreAgentIds(scoreAgentIds);
            if (agentIdMap.size() > scoreAgentIdMap.size()) {
                agentIdMap.forEach((key, agentId) -> {
                    if (scoreAgentIdMap.containsKey(key)) {
                        return;
                    }
                    addAgentScore(principleKey, caseCountKey, month, queue, agentId);
                });
            } else {
                scoreAgentIdMap.forEach((key, agentId) -> {
                    if (agentIdMap.containsKey(key)) {
                        return;
                    }
                    deleteByAgentId(principleKey, caseCountKey, agentId);
                });
            }
            redisCluster.expire(principleKey, expireTime);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            lockService.unLock(ASSIGN_INIT_LOCK, principleKey, lockValue);
        }
    }

    private void addAgentScore(String principle, String caseCountKey, String month, String queue, Long agentId) {
        addPrincipleByAgentId(principle, month, queue, agentId);
        addAssignedCountByAgentId(caseCountKey, month, queue, agentId);
    }

    private String getCaseCountKey(String month, String queue) {
        return getPrincipleKey(month, queue) + "_case_count";
    }

    private void deleteByAgentId(String assignNumKey, String caseCountKey, Long agentId) {
        try {
            redisCluster.zrem(assignNumKey, agentId.toString());
            redisCluster.zrem(caseCountKey, agentId.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, Long> convertToMapByScoreAgentIds(Set<String> scoreAgentIds) {
        Map<String, Long> map = new HashMap<>();
        for (String scoreAgentId : scoreAgentIds) {
            map.put(scoreAgentId, Long.valueOf(scoreAgentId));
        }
        return map;
    }

    private Map<String, Long> convertToMapByAgentIds(List<Integer> agentIds) {
        Map<String, Long> map = new HashMap<>();
        for (Integer agentId : agentIds) {
            map.put(String.valueOf(agentId), Long.valueOf(agentId));
        }
        return map;
    }

    private Long getScoreLength(String assignNumKey) {
        try {
            return redisCluster.llen(assignNumKey);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Long choseAgentId(String month, String queue, BigDecimal principalRemain) {
        String principleKey = getPrincipleKey(month, queue);
        String lockValue = queue + "_" + System.currentTimeMillis();
        try {
            boolean lockResult = lockService.lock(ASSIGN_LOCK, principleKey, lockValue, 30);
            if (!lockResult) {
                log.error("could not found choseAgentId lock by {}", queue);
                return null;
            }
            Long agentId = choseMinScoreAgentId(principleKey);
            if (agentId == null) {
                log.warn("codeAssignNumSupport could not choose agent, please init redis agent rank");
                return null;
            }
            redisCluster.zincrby(principleKey, principalRemain.doubleValue(), agentId.toString());
            redisCluster.expire(principleKey, expireTime);
            return agentId;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            lockService.unLock(ASSIGN_LOCK, principleKey, lockValue);
        }
    }

    private String getPrincipleKey(String month, String queue) {
        return getPreKey() + buildKey(month, queue);
    }

    private Long choseMinScoreAgentId(String principleKey) {
        try {
            Set<String> set = redisCluster.zrange(principleKey, 0, 1);
            if (set == null) {
                return null;
            }
            for (String value : set) {
                return Long.valueOf(value);
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Long choseAgentWithCaseCount(String month, String queue, BigDecimal principalRemain) {
        String principleKey = getPrincipleKey(month, queue);
        String caseCountKey = getCaseCountKey(month, queue);
        String lockValue = queue + "_" + System.currentTimeMillis();
        try {
            boolean lockResult = lockService.lock(ASSIGN_LOCK, principleKey, lockValue, 30);
            boolean countLockResult = lockService.lock(CASE_COUNT_LOCK, caseCountKey, lockValue, 30);
            if (!lockResult || !countLockResult) {
                log.error("could not found choseAgentId lock by {}", queue);
                return null;
            }
            Long agentId = choseMinScoreAgentIdWithCaseCount(principleKey, caseCountKey);
            if (agentId == null) {
                log.error("codeAssignNumSupport could not choose agent, please init redis agent rank");
                return null;
            }
            redisCluster.zincrby(principleKey, principalRemain.doubleValue(), agentId.toString());
            redisCluster.expire(principleKey, expireTime);
            redisCluster.zincrby(caseCountKey, 1, agentId.toString());
            redisCluster.expire(caseCountKey, expireTime);
            return agentId;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            lockService.unLock(ASSIGN_LOCK, principleKey, lockValue);
            lockService.unLock(CASE_COUNT_LOCK, caseCountKey, lockValue);
        }
    }

    private Long choseMinScoreAgentIdWithCaseCount(String principleKey, String caseCountKey) {
        try {
            BigDecimal maxPrinciple = getMaxScore(principleKey);
            BigDecimal maxCaseCount = getMaxScore(caseCountKey);
            if (maxPrinciple == null || maxCaseCount == null) {
                return null;
            }

            if (maxPrinciple.compareTo(BigDecimal.ZERO) == 0 || maxCaseCount.compareTo(BigDecimal.ZERO) == 0) {
                return choseMinScoreAgentId(principleKey);
            }

            Map<Long, BigDecimal> agentIdPrincipleMap = getAgentScoreMap(principleKey, maxPrinciple);
            Map<Long, BigDecimal> agentIdCaseCountMap = getAgentScoreMap(caseCountKey, maxCaseCount);
            log.info("[分案] principleMap:[{}], caseCountMap:[{}]", agentIdPrincipleMap, agentIdCaseCountMap);

            List<AssignScore> assignScoreList = generateAssignScoreList(agentIdPrincipleMap, agentIdCaseCountMap, maxPrinciple, maxCaseCount);
            if (CollectionUtils.isEmpty(assignScoreList)) {
                return null;
            }
            AssignScore minAgent = assignScoreList.stream().min(Comparator.comparing(AssignScore::getScore)).get();
            log.info("[分案] AssignAvgSupport: minAgent [{}] assignScoreList:[{}]", minAgent, assignScoreList);
            return minAgent.getAgentId();
        } catch (Exception e) {
            log.error("[分案] AssignAvgSupport 选择最小催收员异常:[{}]", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private List<AssignScore> generateAssignScoreList(
            Map<Long, BigDecimal> agentIdPrincipleMap, Map<Long, BigDecimal> agentIdCaseCountMap,
            BigDecimal maxPrinciple, BigDecimal maxCount
    ) {
        List<AssignScore> assignScoreList = new ArrayList<>();
        for (Map.Entry<Long, BigDecimal> entry : agentIdPrincipleMap.entrySet()) {
            Long agentId = entry.getKey();
            BigDecimal principleScore = entry.getValue();
            BigDecimal caseCountScore = agentIdCaseCountMap.getOrDefault(agentId, BigDecimal.ONE);

            BigDecimal principleProportion = principleScore.divide(maxPrinciple, 5, RoundingMode.HALF_UP);
            BigDecimal countProportion = caseCountScore.divide(maxCount, 5, RoundingMode.HALF_UP);
            BigDecimal score = principleProportion.add(countProportion);

            AssignScore assignScore = AssignScore.builder()
                    .agentId(agentId)
                    .amount(principleScore)
                    .caseCount(caseCountScore.longValue())
                    .score(score)
                    .build();
            assignScoreList.add(assignScore);
        }
        return assignScoreList;
    }

    private BigDecimal getMaxScore(String key) throws RedisException {
        Set<String> set = redisCluster.zrange(key, -1, -1);
        if (set == null || set.isEmpty()) {
            return null;
        }
        String agentId = set.stream().findFirst().get();
        Double score = redisCluster.zscore(key, agentId);
        if (score == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(score);
    }

    private Map<Long, BigDecimal> getAgentScoreMap(String key, BigDecimal maxScore) throws RedisException {
        Set<Tuple> principleScoreSet = redisCluster.zrevrangeByScoreWithScores(key, maxScore.doubleValue(), 0, 0, -1);
        return principleScoreSet.stream()
                .map(this::convertToAssignScore)
                .collect(Collectors.toMap(AssignScore::getAgentId, AssignScore::getScore));
    }

    private AssignScore convertToAssignScore(Tuple tuple) {
        String binaryString = new String(tuple.getBinaryElement(), StandardCharsets.UTF_8);
        Long agentId = Long.valueOf(binaryString);
        return AssignScore.builder()
                .agentId(agentId)
                .score(BigDecimal.valueOf(tuple.getScore()))
                .build();
    }

    private Set<String> getAllAgentId(String assignNumKey) {
        try {
            return redisCluster.zrange(assignNumKey, 0, -1);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void addPrincipleByAgentId(String assignNumKey, String month, String queue, Long agentId) {
        try {
            Double score = redisCluster.zscore(assignNumKey, agentId.toString());
            if (score != null && score > 0) {
                log.info("assignNumKey {} has add agentId {}", assignNumKey, agentId);
                return;
            }
            BigDecimal initScore = getInitScoreByAgentId(month, queue, agentId);
            if (initScore == null) {
                initScore = BigDecimal.ZERO;
            }
            redisCluster.zincrby(assignNumKey, initScore.doubleValue(), agentId.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void addAssignedCountByAgentId(String caseCountKey, String month, String queue, Long agentId) {
        try {
            Double score = redisCluster.zscore(caseCountKey, agentId.toString());
            if (score != null && score > 0) {
                log.info("assignNumKey {} has add agentId {}", caseCountKey, agentId);
                return;
            }
//            String month = new SimpleDateFormat("yyyy-MM").format(new Date());
            Long initScore = getAssignedCountByAgentId(month, queue, agentId);
            if (initScore == null) {
                initScore = 0L;
            }
            redisCluster.zincrby(caseCountKey, initScore.doubleValue(), agentId.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public boolean hasMember(String month, String queue, Long agentId) {
        try {
            String assignNumKey = getPrincipleKey(month, queue);
            Double score = redisCluster.zscore(assignNumKey, agentId.toString());
            if (score != null && score > 0) {
                log.info("assignNumKey {} has add agentId {}", assignNumKey, agentId);
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public int getExpireSecond() {
        long now = System.currentTimeMillis();
        long endDate = DateUtils.getDayEnd(new Date()).getTime();
        return (int) ((endDate - now) / 1000);
    }

    public void decreaseAgentNum(String month, String queue, Long agentId, BigDecimal num) {
        try {
            String principleKey = getPrincipleKey(month, queue);
            String caseCountKey = getCaseCountKey(month, queue);
            redisCluster.zincrby(caseCountKey, -1, agentId.toString());
            redisCluster.zincrby(principleKey, -num.doubleValue(), agentId.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void increaseAgentNum(String month, String queue, Long agentId, BigDecimal num) {
        try {
            String assignNumKey = getPrincipleKey(month, queue);
            String caseCountKey = getCaseCountKey(month, queue);
            redisCluster.zincrby(assignNumKey, num.doubleValue(), agentId.toString());
            redisCluster.zincrby(caseCountKey, 1, agentId.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public boolean lockCollection(Long collectionId, String lockValue) {
        return lockService.lock(ASSIGN_COLLECTION_LOCK, collectionId, lockValue, 30);
    }

    public void unLockCollection(Long collectionId, String lockValue) {
        lockService.unLock(ASSIGN_COLLECTION_LOCK, collectionId, lockValue);
    }

    public void deleteByQueueAndMonth(String queue, String month) {
        try {
            redisCluster.del(getPrincipleKey(month, queue));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
