package com.wacai.loan.goblin.service.customer.consomer;

import java.nio.charset.StandardCharsets;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumer;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import com.wacai.gbl.kafka.agent.consumer.Message;
import com.wacai.loan.goblin.loan.service.api.dto.LoanDTO;
import com.wacai.loan.goblin.service.customer.api.CustomerContactFixService;
import com.wacai.loan.goblin.service.customer.api.dto.CustomerContactFixDTO;
import com.wacai.loan.tenant.spring.bean.context.ContextUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * @description:同步信修联系人
 * <AUTHOR>
 * @date 2022-06-02 15:50:39
 * @since JDK 1.8
 */
@Slf4j
public class CustomerContactFixSyncToAmcConsumer extends KafkaConsumer {

    @Autowired
    private CustomerContactFixService customerContactFixService;

    public CustomerContactFixSyncToAmcConsumer(KafkaConsumerConfig consumerConfig) {
        super(consumerConfig);
    }

    @Override
    public void onMessageReceived(Message message) {
        try {
            log.info("CustomerContactFixSyncToAmcConsumer start consume!");
            final String strMsg = new String(message.getValue(), StandardCharsets.UTF_8);
            final JSONObject joMsg = JSON.parseObject(strMsg);
            final String domain =joMsg.getString("domain");
            if (StringUtils.isEmpty(domain)) {
                log.info("CustomerContactSyncToAmcConsumer domain is null!!");
            }
            ContextUtils.set(domain);
            LoanDTO loanDTO = JSON.parseObject(joMsg.getString("loanDTO"), LoanDTO.class);
            CustomerContactFixDTO customerContactFixDTO = JSON.parseObject(joMsg.getString("customerContactFixDTO"), CustomerContactFixDTO.class);
            customerContactFixService.syncContactFixToAmc(loanDTO, customerContactFixDTO);
        } catch (Exception e) {
            log.error("CustomerContactFixSyncToAmcConsumer error , message is : [{}] , exception is [{}]", new String(message.getValue()), e);
        }
    }
}
