package com.wacai.loan.goblin.service.assign.consumer;

import com.alibaba.fastjson.JSONObject;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumer;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import com.wacai.gbl.kafka.agent.consumer.Message;
import com.wacai.loan.goblin.service.assign.api.QueueProcessorMap;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * <AUTHOR>
 * @date 2019/6/6 下午5:11.
 */
@Slf4j
public class QueueProcessConsumer extends KafkaConsumer implements InitializingBean {

    @Autowired
    private QueueProcessorMap queueProcessorMap;

    private List<ConcurrentLinkedQueue<AssignmentDTO>> concurrentLinkedQueues = new ArrayList<>();

    @Value("${queue.processor.thread.num:8}")
    private int queueProcessorThreadNum;

    public QueueProcessConsumer(KafkaConsumerConfig consumerConfig) {
        super(consumerConfig);
    }


    @Override
    public void onMessageReceived(Message message) {
        String messageJson = new String(message.getValue());
        try {
            AssignmentDTO assignmentDTO = JSONObject.parseObject(messageJson, AssignmentDTO.class);
            log.info("分人消息信息offset:{}, queue:{}, collectionId:{},strategyId:{} ", message.getOffset(), assignmentDTO.getQueue(),assignmentDTO.getCollectionId(), assignmentDTO.getStrategyId());
            Long collectionId = assignmentDTO.getCollectionId();
            int queueIndex = (int) (collectionId % queueProcessorThreadNum);
            concurrentLinkedQueues.get(queueIndex).add(assignmentDTO);
        } catch (Exception e) {
            log.error("QueueProcessConsumer onMessageReceived by {} error: ", messageJson, e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (int num = 0; num < queueProcessorThreadNum; num++) {
            ConcurrentLinkedQueue<AssignmentDTO> concurrentLinkedQueue = new ConcurrentLinkedQueue<>();
            concurrentLinkedQueues.add(concurrentLinkedQueue);
            String name = "QueueProcessorRunnable_" + num;
            QueueProcessorRunnable queueProcessorRunnable = new QueueProcessorRunnable(name, concurrentLinkedQueue);
            queueProcessorRunnable.start();
        }
    }

    class QueueProcessorRunnable extends Thread {

        private int maxBatchSize = 5000;

        private int maxWaitTimes = 20;

        private int waitTimes;

        private ConcurrentLinkedQueue<AssignmentDTO> concurrentLinkedQueue;

        private List<AssignmentDTO> assignmentDTOList = new ArrayList<>();

        private String name;

        public QueueProcessorRunnable(String name, ConcurrentLinkedQueue<AssignmentDTO> concurrentLinkedQueue) {
            this.name = name;
            this.concurrentLinkedQueue = concurrentLinkedQueue;
        }

        @Override
        public void run() {
            while (true) {
                try {

                    AssignmentDTO assignmentDTO = concurrentLinkedQueue.poll();
                    if (assignmentDTO != null) {
                        assignmentDTOList.add(assignmentDTO);
                    }
                    if (!isAllowProcessor()) {
                        if (assignmentDTO == null) {
                            Thread.sleep(1000L);
                            waitTimes++;
                        }
                        continue;
                    }
                    doProcessor();
                } catch (Exception e) {
                    log.error("QueueProcessorRunnable thread error : ", e);
                }
            }
        }

        private void doProcessor() {
            try {
                String uuid = UUID.randomUUID().toString();
                long startTime = System.currentTimeMillis();
                log.info("{}-{} doProcessor start and assignmentDTOList is {}", name, uuid, assignmentDTOList.size());
                queueProcessorMap.streamingProcessQueue(assignmentDTOList);
                long costTime = System.currentTimeMillis() - startTime;
                log.info("{}-{} doProcessor finish and assignmentDTOList is {} and cost {}ms", name, uuid, assignmentDTOList.size(), costTime);
            } catch (Exception e) {
                log.error("QueueProcessorRunnable doProcessor by assignmentDTOList.size{} and error : ", assignmentDTOList.size(), e);
            } finally {
                assignmentDTOList.clear();
            }
        }

        private boolean isAllowProcessor() {
            if (assignmentDTOList.size() == 0) {
                return false;
            }
            if (assignmentDTOList.size() >= maxBatchSize) {
                waitTimes = 0;
                return true;
            }
            if (waitTimes >= maxWaitTimes) {
                waitTimes = 0;
                return true;
            }
            return false;
        }

    }

}
