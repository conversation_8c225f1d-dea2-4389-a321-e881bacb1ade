package com.wacai.loan.goblin.service.workflow.core;

import com.wacai.loan.goblin.common.util.BeanCopier;
import com.wacai.loan.goblin.service.workflow.api.WorkflowTaskService;
import com.wacai.loan.goblin.service.workflow.api.dto.WorkflowTaskDTO;
import com.wacai.loan.goblin.service.workflow.dao.po.WorkflowTask;
import com.wacai.loan.goblin.service.workflow.dao.repository.WorkflowTaskRepository;
import com.wacai.loan.tenant.spring.bean.annotation.TenantConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/10/11
 */
@Service
@TenantConfig
public class DefaultWorkflowTaskService implements WorkflowTaskService {

    @Autowired
    private WorkflowTaskRepository workflowTaskRepository;

    @Override
    public List<WorkflowTaskDTO> getAllByResourceCodeNotNull() {
        List<WorkflowTask> taskList = workflowTaskRepository.getAllByResourceCodeNotNull();
        return taskList.stream().map(task -> {
            WorkflowTaskDTO workflowTaskDTO = new WorkflowTaskDTO();
            BeanCopier.copyProperties(task, workflowTaskDTO);
            return workflowTaskDTO;
        }).collect(Collectors.toList());
    }
}
