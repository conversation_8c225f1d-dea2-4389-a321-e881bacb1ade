package com.wacai.loan.goblin.service.strategy.core;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wacai.gbl.util.GblAlertUtil;
import com.wacai.loan.goblin.common.util.DateUtils;
import com.wacai.loan.goblin.service.assign.core.ValueExtractorV2;
import com.wacai.loan.goblin.service.collection.api.CollectionService;
import com.wacai.loan.goblin.service.collection.api.CollectionTagService;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import com.wacai.loan.goblin.service.collection.api.dto.CollectionDTO;
import com.wacai.loan.goblin.service.collection.api.dto.CollectionTagTypeDTO;
import com.wacai.loan.goblin.service.feature.api.CaseFeatureService;
import com.wacai.loan.goblin.service.feature.api.dto.FactorValueDTO;
import com.wacai.loan.goblin.service.strategy.core.support.*;
import com.wacai.loan.medivh.api.constants.ThresholdType;
import com.wacai.loan.medivh.api.dto.CandidateDTO;
import com.wacai.pt.redalert.api.common.BusinessUnit;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * 2018/8/9
 */
@Component
public class AssignmentFactorCollectService implements BeanPostProcessor {
    @Autowired
    private ValueExtractorV2 valueExtractorV2;

    @Autowired
    private CollectionTagService collectionTagService;

//    private Map<String, FactorCollector> collectorscollectors = new HashMap<>();

    private static final ThreadLocal<CollectionDTO> COLLECTION_CACHE = new ThreadLocal<>();

    private static final ThreadLocal<EsCaseFactorDTO> ES_CASE_FACTOR_CACHE = new ThreadLocal<>();

    public static void setCache(CollectionDTO collectionDTO) {
        COLLECTION_CACHE.set(collectionDTO);
    }

    public static CollectionDTO get() {
        return COLLECTION_CACHE.get();
    }

    private static List<CandidateDTO> booleanCandidates;

    private static List<CandidateDTO> collectionModeCandidates;

    private static List<CandidateDTO> riskLevelCandidates;

    private static Set<Class> realTimeFactors = new HashSet<>();

    static {
        booleanCandidates = JSON
                .parseArray("[{\"label\":\"是\",\"value\":\"true\"},{\"label\":\"否\",\"value\":\"false\"}]",
                        CandidateDTO.class);
        collectionModeCandidates = JSON
                .parseArray("[{\"label\":\"新模式\",\"value\":\"new\"},{\"label\":\"老模式\",\"value\":\"old\"}]",
                        CandidateDTO.class);
        riskLevelCandidates = null;
    }

    /**
     * 通过ES获取因子
     * 不论是批量执行还是流式执行
     * 1.若collectionId对应ES数据或某个因子数据不存在，就重新计算
     * 2.若ES数据存在，就从ES获取因子值，非当日更新的数据也可以使用（因为es的延迟不可能那么大）
     * @param collectionDTO
     * @param oldAssignmentDTO
     * @param jobDate
     * @return
     */
    public Map<String, String> collect(CollectionDTO collectionDTO, AssignmentDTO oldAssignmentDTO, Date jobDate){
        Map<String, String> factorNameValueMap = buildFactorNameValueMap(collectionDTO, oldAssignmentDTO, jobDate);
//        for (FactorCollector collector : collectors.values()) {
//            factorNameValueMap.put(collector.getKey(), collector.getValue(String.valueOf(collectionDTO.getId())));
//        }

        //案件标签对应的策略因子
        Map<String, String> collectionTagFactorNameValueMap = buildCollectionTagFactorNameValueMap(collectionDTO);
        if (!CollectionUtils.isEmpty(collectionTagFactorNameValueMap)) {
        	factorNameValueMap.putAll(collectionTagFactorNameValueMap);
		}
        return factorNameValueMap;
    }

    /**
     * 实时计算因子
     * 对于日间分案，实时计算所有因子
     * @param collectionDTO
     * @param oldAssignmentDTO
     * @param jobDate
     * @return
     */
    public Map<String, String> collectRealtimeFactor(CollectionDTO collectionDTO, AssignmentDTO oldAssignmentDTO, Date jobDate) {
        Map<String, String> factorNameValueMap = buildFactorNameValueMap(collectionDTO, oldAssignmentDTO, jobDate);
//        for (FactorCollector collector : collectors.values()) {
//            try {
//                factorNameValueMap.put(collector.getKey(), queryFactorValue(collectionDTO, collector.getKey(), jobDate));
//            } catch (Exception e) {
//                throw new RuntimeException( collectionDTO.getId() + " collect real factor " + collector.getKey() + " has error", e );
//            }
//        }

        //案件标签对应的策略因子
        Map<String, String> collectionTagFactorNameValueMap = buildCollectionTagFactorNameValueMap(collectionDTO);
        if (!CollectionUtils.isEmpty(collectionTagFactorNameValueMap)) {
        	factorNameValueMap.putAll(collectionTagFactorNameValueMap);
		}
        return factorNameValueMap;
    }

    /**
     * 批量获取key集合的因子
     * 其它策略可使用该方法复用分案因子以及取数逻辑
     * @param collectionDTO
     * @param keys 因子的key
     * @param jobDate 获取时间
     * @param isRealTimeValue 是否实时
     * @return
     */
    public Map<String, String> collectByFactorKeys(CollectionDTO collectionDTO, Set<String> keys, Date jobDate, boolean isRealTimeValue){
        Map<String, String> result = Maps.newHashMap();
        for(String key : keys) {
            String value = null;//getValue(key, collectionDTO, jobDate, isRealTimeValue);
            result.put( key, value);
        }
        return result;
    }

//    private String getValue(String key, CollectionDTO collectionDTO, Date jobDate, boolean isRealtimeValue){
//        if(isRealtimeValue) {
//            return queryFactorValue(collectionDTO, key, jobDate);
//        } else {
////            FactorCollector factorCollector = collectors.get(key);
////            if(factorCollector != null) {
////                return factorCollector.getValue(String.valueOf(collectionDTO.getId()));
////            } else {
////                return queryFactorValue(collectionDTO, key, jobDate);
////            }
//        }
//    }

    private Map<String, String> buildFactorNameValueMap(CollectionDTO collectionDTO, AssignmentDTO oldAssignmentDTO, Date jobDate){
        Map<String, String> factorNameValueMap = new HashMap<>(64);

        String dayPastDue = queryFactorValue(collectionDTO, "DAYS_PAST_DUE", jobDate);
        factorNameValueMap.put("DAYS_PAST_DUE", dayPastDue);
        String flowRatePhase = queryFactorValue(collectionDTO, "FLOW_RATE_PHASE", jobDate);
        factorNameValueMap.put("FLOW_RATE_PHASE", flowRatePhase);
        String dueDate = queryFactorValue(collectionDTO, "DUE_DATE", jobDate);
        factorNameValueMap.put("DUE_DATE", dueDate);
        if(oldAssignmentDTO != null) {
            factorNameValueMap.put("ASSIGNMENT_TIME", DateUtils.formatDate(DateUtils.TimeType.yyyy_MM_ddHHmmss, oldAssignmentDTO.getCreatedTime()));
        }

        return factorNameValueMap;
    }

    /**
     * buildCollectionTagFactorNameValueMap:构建案件标签对应的策略因子
     * @param collectionDTO
     * @return
     * <AUTHOR>
     * @date 2021-07-14 16:49:12
     */
	private Map<String, String> buildCollectionTagFactorNameValueMap(CollectionDTO collectionDTO) {

		Map<String, String> collectionTagFactorNameValueMap = null;

		List<CollectionTagTypeDTO> collectionTagTypeList = collectionTagService.getAllCollectionTagTypeList();
		if (CollectionUtils.isEmpty(collectionTagTypeList)) {
			return collectionTagFactorNameValueMap;
		}

		collectionTagFactorNameValueMap = new HashMap<>(64);

		for (CollectionTagTypeDTO collectionTagTypeDTO : collectionTagTypeList) {
			// 剔除已经存在的策略因子，避免重复查询
//			if (collectors.containsKey(collectionTagTypeDTO.getTagTypeCode())) {
//				continue;
//			}

			collectionTagFactorNameValueMap.put(collectionTagTypeDTO.getTagTypeCode(), valueExtractorV2
					.getCollectionPropertyValue(collectionDTO.getId(), collectionTagTypeDTO.getTagTypeCode()));
		}

		return collectionTagFactorNameValueMap;
	}

    /**
     * 根据factor key直接定位到value
     * @param collectionDTO
     * @param factorKey
     * @param jobDate
     * @return
     */
    private String queryFactorValue(CollectionDTO collectionDTO, String factorKey, Date jobDate){
        Object value = valueExtractorV2.getValue(collectionDTO, factorKey, jobDate);
        return value != null ? value.toString() : null;
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof AssignmentFactorCollectService.AbstractAssignmentFactorCollector && !realTimeFactors.contains(bean.getClass())) {
//            FactorCollector collector = (FactorCollector) bean;
//            if (org.springframework.util.StringUtils.isEmpty(collector.getKey())) {
//                throw new RuntimeException("bean FactorCollector.getFactorName must not return null or empty");
//            }
//            if (collectors.containsKey(collector.getKey())) {
//                throw new RuntimeException(String.format("duplicate collector for [%s]", collector.getKey()));
//            }
//            collectors.put(collector.getKey(), collector);
        }
        if(bean instanceof ValueExtractorV2){
            System.out.println("dddd");
        }
        return bean;
    }


    // 以下是手写的因子
    public static abstract class AbstractAssignmentFactorCollector  {
        @Autowired
        private CollectionService collectionService;
        @Autowired
        private CaseFeatureService caseFeatureService;
        @Autowired
        protected ValueExtractorV2 valueExtractor;

       // @JasmineValue(notEmpty = false, defaultValue = "false", key = "goblin.job.assign.EsFactorValue.enable", subModule = JasmineConfig.GOBLIN_JOB, probe = true)
        @Value("${goblin.job.assign.EsFactorValue.enable:false}")
        private boolean enableEsFactorValue ;

       // @JasmineValue(notEmpty = false, key = "strategy.medivh.bu", defaultValue = "goblin", subModule = JasmineConfig.GOBLIN_JOB, probe = true)
        private volatile String bu = "goblin";

        //@JasmineValue(notEmpty = false, key = "strategy.medivh.app", defaultValue = "assignment", subModule = JasmineConfig.GOBLIN_JOB, probe = true)
        private volatile String app = "assignment";

        public String getBu() {
            return bu;
        }

        public String getApp() {
            return app;
        }

        public String getValue(String caseId) {
            if (enableEsFactorValue) {
                if (ES_CASE_FACTOR_CACHE.get() == null || !ES_CASE_FACTOR_CACHE.get().getCaseId().equals(caseId)) {
                    long startTime = System.currentTimeMillis();
                    FactorValueDTO factorValueDTO = caseFeatureService.getFactorValue(caseId);

                   /* GblAlertUtil.build(BusinessUnit.loan, "goblin-job")
                            .addDatabase("com.wacai.loan.goblin")
                            .addMeasurement("goblin-job-assign-es-period")
                            .addField("collectionId", caseId)
                            .addField("period", System.currentTimeMillis() - startTime)
                            .produce();*/

                    EsCaseFactorDTO esCaseFactorDTO = new EsCaseFactorDTO();
                    esCaseFactorDTO.setCaseId(caseId);
                    esCaseFactorDTO.setFactorValueDTO(factorValueDTO);
                    ES_CASE_FACTOR_CACHE.set(esCaseFactorDTO);
                }
                Map<String, String> factorNameValueMap = ES_CASE_FACTOR_CACHE.get().getFactorValueDTO().getData();
                String key = null;//getKey();
                return factorNameValueMap.containsKey(key) ? factorNameValueMap.get(key) : requeryValue(caseId);
            }
            return requeryValue(caseId);
        }

        private String requeryValue(String caseId) {
            /*GblAlertUtil.build(BusinessUnit.loan, "goblin-job")
                    .addDatabase("com.wacai.loan.goblin")
                    .addMeasurement("goblin-job-assign-requeryFactor")
                    .addTag("factor", this.getClass().getSimpleName())
                    .addField("collectionId", caseId)
                    .produce();*/
            if (COLLECTION_CACHE.get() == null || !String.valueOf(COLLECTION_CACHE.get().getId()).equals(caseId)) {
                CollectionDTO collectionDTO = collectionService.findById(Long.valueOf(caseId));
                COLLECTION_CACHE.set(collectionDTO);
            }

            return doRequeryValue(caseId);
        }

        /**
         * 重新计算因子
         *
         * @param id id
         * @return value
         */
        abstract String doRequeryValue(String id);
    }

//    @Component
    static class STOP_COLLECT extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "是否停催";
        }

        public String getKey() {
            return "STOP_COLLECT";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getStopCollect(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public ThresholdType getValueType() {
            return ThresholdType.BOOLEAN;
        }

        public List<CandidateDTO> getCandidateValues() {
            return booleanCandidates;
        }

    }

//    @Component
    static class LAWSUIT extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "诉讼标签";
        }

        public String getKey() {
            return "LAWSUIT";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getLawsuitTag(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray("[{\"label\":\"诉讼调解中\",\"value\":\"PROCESSING\"},{\"label\":\"诉讼调解完成\",\"value\":\"PROCESSED\"}]", CandidateDTO.class);
        }

    }

//    @Component
    static class TONGDUN_OVERDUE_SPIRIT extends AbstractAssignmentFactorCollector {
        public String getName() {
            return "同盾逾期精灵";
        }

        public String getKey() {
            return "TONGDUN_OVERDUE_SPIRIT";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getTongdunOverdueSpirit(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public ThresholdType getValueType() {
            return ThresholdType.BOOLEAN;
        }

        public List<CandidateDTO> getCandidateValues() {
            return booleanCandidates;
        }

    }

    @Component
    static class COMPLAINT extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "敏感易投诉";
        }

        public String getKey() {
            return "COMPLAINT";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.isComplaint(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public ThresholdType getValueType() {
            return ThresholdType.BOOLEAN;
        }

        public List<CandidateDTO> getCandidateValues() {
            return booleanCandidates;
        }

    }

//    @Component
    static class OUTSTANDING extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "委外标记";
        }

        public String getKey() {
            return "OUTSTANDING";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getOutStandingTag(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public ThresholdType getValueType() {
            return ThresholdType.BOOLEAN;
        }

        public List<CandidateDTO> getCandidateValues() {
            return booleanCandidates;
        }

    }

    @Component
    static class CHANNEL extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "渠道";
        }

        @Autowired
        private ChannelCandidateValueObtainer obtainer;

        public String getKey() {
            return "CHANNEL";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getChannel(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray(JSON.toJSONString(obtainer.obtainCandidateValues()), CandidateDTO.class);
        }
    }

    @Component
    static class GROUP extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "分组";
        }

        public String getKey() {
            return "GROUP";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getGroup(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray("[{\"label\":\"A组\",\"value\":\"A\"},{\"label\":\"B组\",\"value\":\"B\"}]", CandidateDTO.class);
        }

    }

    @Component
    static class FLOW_RATE_PHASE extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "逾期阶段";
        }

        @Autowired
        private FlowRatePhaseCandidateValueObtainer obtainer;

        public String getKey() {
            return "FLOW_RATE_PHASE";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getFlowRatePhase(COLLECTION_CACHE.get(), new Date());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            List<Map<String, String>> candidates = obtainer.obtainCandidateValues();
            Map<String, String> origin = new HashMap<>(2);
            origin.put("label", "上次分案值");
            origin.put("value", "${FLOW_RATE_PHASE}");
            candidates.add(origin);
            return JSON.parseArray(JSON.toJSONString(candidates), CandidateDTO.class);
        }
    }

    @Component
    static class NOT_REMIND_4_UNOVERDUE extends AbstractAssignmentFactorCollector {
        public String getName() {
            return "非逾期不提醒";
        }

        public String getKey() {
            return "NOT_REMIND_4_UNOVERDUE";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getNotOverdue(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public ThresholdType getValueType() {
            return ThresholdType.BOOLEAN;
        }

        public List<CandidateDTO> getCandidateValues() {
            return booleanCandidates;
        }

    }

//    @Component
    static class LOAN_TYPE extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "贷种";
        }

        @Autowired
        private LoanTypeCandidateValueObtainer obtainer;

        public String getKey() {
            return "LOAN_TYPE";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getLoanType(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray(JSON.toJSONString(obtainer.obtainCandidateValues()), CandidateDTO.class);
        }

        public ThresholdType getValueType() {
            return ThresholdType.NUMBER;
        }

    }

    @Component
    static class LOAN_SOURCE_CODE extends AbstractAssignmentFactorCollector {

        public String getName() {
            return "贷种编码";
        }

        @Autowired
        private LoanSourceCodeCandidateValueObtainer obtainer;

        public String getKey() {
            return "LOAN_SOURCE_CODE";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getLoanSourceCode(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray(JSON.toJSONString(obtainer.obtainCandidateValues()), CandidateDTO.class);
        }

        public ThresholdType getValueType() {
            return ThresholdType.STRING;
        }

    }

    @Component
    static class PRODUCT_TYPE extends AbstractAssignmentFactorCollector {
        public String getName() {
            return "产品类型";
        }

        @Autowired
        private ProductTypeCandidateValueObtainer obtainer;

        public String getKey() {
            return "PRODUCT_TYPE";
        }

        @Override
        public String doRequeryValue(String id) {
            Object value = valueExtractor.getProduceType(COLLECTION_CACHE.get());
            return value == null ? null : value.toString();
        }

        public List<CandidateDTO> getCandidateValues() {
            return JSON.parseArray(JSON.toJSONString(obtainer.obtainCandidateValues()), CandidateDTO.class);
        }

    }


}
