package com.wacai.loan.goblin.service.report.dao.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.kafka.common.protocol.types.Field;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/*
 * 催收员分案详情统计
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AssignmentReportList implements Serializable {
    private static final long serialVersionUID = -3786488971900204092L;



    private Long loanTotal;

    private String loanSourceId;

    private String loanSourceName;
    private String queueCode;
    private String queueName;



    private String groupCode;
    private String groupName;


    private String flag;



}
