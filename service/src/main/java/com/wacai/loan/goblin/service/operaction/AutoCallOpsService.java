package com.wacai.loan.goblin.service.operaction;

import java.util.Date;

import com.wacai.loan.tenant.spring.bean.annotation.TenantConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.wacai.loan.goblin.common.constant.Constant;
import com.wacai.loan.goblin.common.constant.IVRCallResultEnum;
import com.wacai.loan.goblin.common.constant.InteractionConclusion;
import com.wacai.loan.goblin.service.assign.api.AssignEngineV3;
import com.wacai.loan.goblin.service.collection.api.CollectionService;
import com.wacai.loan.goblin.service.collection.api.InteractionExtActionService;
import com.wacai.loan.goblin.service.collection.api.InteractionService;
import com.wacai.loan.goblin.service.collection.api.dto.CollectionDTO;
import com.wacai.loan.goblin.service.collection.api.dto.InteractionCallValid;
import com.wacai.loan.goblin.service.collection.api.dto.InteractionDTO;
import com.wacai.loan.goblin.service.collection.api.dto.InteractionExtActionDTO;
import com.wacai.loan.goblin.service.collection.api.dto.InteractionType;
import com.wacai.loan.goblin.service.operation.api.OpHandlerService;
import com.wacai.loan.goblin.service.operation.api.dto.OpActionDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: doudai
 * 2020/7/30
 */
@Service("AUTO_CALL")
@Slf4j
@TenantConfig
public class AutoCallOpsService implements OpHandlerService {
	
	@Autowired
	private InteractionService interactionService;

	@Autowired
	private InteractionExtActionService interactionExtActionService;

    @Autowired
    AssignEngineV3 assignEngineV3;

    @Autowired
    CollectionService collectionService;

    @Override
    public void handleInteraction(OpActionDTO opActionDTO) {
    	
		try {
			
			CollectionDTO collectionDTO = collectionService.findByLoanId(opActionDTO.getLoanId());
			
			InteractionDTO interactionDTO = interactionService.getTodayInteraction(collectionDTO.getId(),
					Constant.SYSTEM_OPERATOR);
			
			//保存催记外部动作关联信息
			saveInteractionExtAction(opActionDTO, interactionDTO);
			
			interactionDTO.setType(InteractionType.AUTO_CALL);// 自动语音外呼
			interactionDTO.setResult(opActionDTO.getResult().getStateInfo());
			
			if(IVRCallResultEnum.ANSWER == opActionDTO.getResult()){
				interactionDTO.setConclusion(InteractionConclusion.IN_COMMUNICATION);
                interactionDTO.setResult(InteractionConclusion.IN_COMMUNICATION.getValue());
			}else {
				interactionDTO.setPhoneStatus(opActionDTO.getResult().getStateInfo());
	            interactionDTO.setIsCallValid(InteractionCallValid.VALID_NO);
	            interactionDTO.setResult(InteractionConclusion.UNCONNECTED.getValue());
	            interactionDTO.setConclusion(InteractionConclusion.UNCONNECTED);
			}
			//更新催收记录
			interactionService.handleInteraction(interactionDTO);
			
		} catch (Exception e) {
			log.error("XH_AUTO_CALL handleInteraction loanId = {}", opActionDTO.getLoanId(), e);
		}
    }

    /**
     * saveInteractionExtAction:保存催记外部动作关联信息
     * @param opActionDTO
     * @param interactionDTO
     * <AUTHOR>
     * @date 2020-08-10 15:21:07
     */
	private void saveInteractionExtAction(OpActionDTO opActionDTO, InteractionDTO interactionDTO) {
		InteractionExtActionDTO interactionExtActionDTO = new InteractionExtActionDTO();
		interactionExtActionDTO.setCreatedBy(Constant.SYSTEM_OPERATOR);
		interactionExtActionDTO.setUpdatedBy(Constant.SYSTEM_OPERATOR);
		interactionExtActionDTO.setInteractionId(interactionDTO.getId());
		interactionExtActionDTO.setThirdPartyId(Long.valueOf(opActionDTO.getActionId()));
		interactionExtActionDTO.setThirdPartyType(opActionDTO.getActionType());
		interactionExtActionDTO.setThirdPartyTypeGroup(opActionDTO.getActionTypeGroup());
		
		interactionExtActionDTO = interactionExtActionService.updateInteractionExtAction(interactionExtActionDTO);
	}

    @Override
    public void afterInteraction(OpActionDTO opActionDTO) {
        if(IVRCallResultEnum.ANSWER == opActionDTO.getResult()){
            Long loanId = opActionDTO.getLoanId();
            CollectionDTO collectionDTO  = collectionService.findByLoanId(loanId);

            if(collectionDTO == null) {
                log.error("cannot find loanId {} in collection", loanId);
                return;
            }

            log.info("AutoCall operation invoke assign loanId {} collectionId {} result is {}",
                    loanId, collectionDTO.getId(), opActionDTO.getResult());
            assignEngineV3.acquireLockAndMedivhAssign(collectionDTO.getId(), new Date(), null);
        } else {
            log.info("AutoCall operation loan id {} result is {}", opActionDTO.getLoanId(), opActionDTO.getResult());
        }

    }
}
