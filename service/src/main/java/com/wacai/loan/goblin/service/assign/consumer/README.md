# Disruptor队列处理器

## 概述

基于LMAX Disruptor框架实现的高性能队列处理器，用于替代原有的基于`ConcurrentLinkedQueue`的实现。

## 主要优势

### 1. 性能提升
- **无锁设计**：使用环形缓冲区和CAS操作，避免锁竞争
- **批处理优化**：智能批处理机制，提高吞吐量
- **内存友好**：减少GC压力，提高内存利用率
- **CPU缓存友好**：顺序访问模式，提高缓存命中率

### 2. 数据安全
- **失败重试**：自动重试机制，处理临时性失败
- **死信队列**：持久化处理失败的消息
- **详细日志**：完整的失败记录和审计日志
- **优雅关闭**：确保处理中的消息不丢失

### 3. 可观测性
- **性能指标**：实时统计处理速度、成功率等
- **健康检查**：系统状态监控和告警
- **配置管理**：灵活的参数调整

### 4. 易维护性
- **模块化设计**：职责分离，代码结构清晰
- **配置驱动**：通过配置文件调整行为
- **测试友好**：完整的单元测试和性能测试

## 架构设计

```
Kafka Message → DisruptorQueueProcessConsumer → Disruptor RingBuffer → BatchEventHandler → QueueProcessorMap
                                                      ↓
                                              FailureHandlingService → DeadLetterQueue/Logs/Alerts
```

## 配置说明

### 启用Disruptor实现
```properties
# 启用Disruptor队列处理器
queue.processor.use.disruptor=true
```

### 核心配置
```properties
# 环形缓冲区大小（必须是2的幂）
queue.processor.buffer.size=65536

# 消费者线程数
queue.processor.thread.num=8

# 批处理大小
queue.processor.batch.size=1000

# 批处理超时时间（毫秒）
queue.processor.batch.timeout.ms=100
```

### 失败处理配置
```properties
# 启用失败日志
queue.processor.failure.log.enabled=true

# 失败日志路径
queue.processor.failure.log.path=/tmp/goblin/failed-assignments

# 启用死信队列
queue.processor.dead.letter.enabled=true

# 最大重试次数
queue.processor.retry.max=3
```

## 性能调优指南

### 1. 缓冲区大小调优
- **高吞吐量场景**：131072 (128K) 或更大
- **低延迟场景**：16384 (16K) 或更小
- **内存受限**：8192 (8K)

### 2. 线程数调优
- **CPU密集型**：CPU核心数
- **IO密集型**：CPU核心数 × 2
- **混合型**：CPU核心数 × 1.5

### 3. 批处理调优
- **高吞吐量**：batch.size=1000-5000, timeout=200-500ms
- **低延迟**：batch.size=100-500, timeout=50-100ms
- **平衡**：batch.size=500-1000, timeout=100-200ms

## 监控和运维

### 1. 定时监控日志
系统采用定时扫描方式进行监控，通过日志输出监控信息：

**监控频率：**
- **性能监控**：每5分钟执行一次
- **健康检查**：每10分钟执行一次
- **告警检查**：每30分钟执行一次

**日志级别配置：**
```properties
# 监控日志级别：DEBUG(详细), INFO(简要), WARN(仅告警)
queue.processor.monitor.log.level=INFO
```

**日志输出示例：**
```
# INFO级别 - 只在有处理活动时输出
[2024-12-19 14:30:00] DisruptorConsumer - Processed: +1250 (4.2/s), Failed: +5, Success Rate: 99.60%, Buffer: 15.3%

# DEBUG级别 - 详细输出所有指标
[2024-12-19 14:30:00] DisruptorConsumer Performance - Total: P:12500 Pr:12450 F:50, Delta: +1250 (+1245 +5), Rate: 4.1/s, Success: 99.60%, Buffer: 15.3%

# 告警日志
WARN [2024-12-19 14:30:00] WARNING ALERT - High failure rate: 12.50% (Total: 1000, Failed: 125)
ERROR [2024-12-19 14:30:00] CRITICAL ALERT - Very high failure rate: 25.00% (Total: 1000, Failed: 250)
```

### 2. REST接口（手动查询）
```bash
# 获取统计信息
curl http://localhost:8080/queue-process-monitor/statistics

# 健康检查
curl http://localhost:8080/queue-process-monitor/health

# 配置信息
curl http://localhost:8080/queue-process-monitor/config
```

### 3. 关键指标
- **publishedCount**：发布到Disruptor的消息数
- **processedCount**：成功处理的消息数
- **failedCount**：处理失败的消息数
- **bufferUtilization**：缓冲区使用率
- **successRate**：成功率
- **failureRate**：失败率

### 4. 告警规则
- 失败率 > 20%：CRITICAL告警（ERROR级别日志）
- 失败率 > 10%：WARNING告警（WARN级别日志）
- 缓冲区使用率 > 80%：容量告警（WARN级别日志）

## 故障排查

### 1. 常见问题
- **内存不足**：减少buffer.size或增加JVM堆内存
- **处理延迟高**：减少batch.size或batch.timeout.ms
- **CPU使用率高**：减少thread.num或优化业务逻辑

### 2. 日志分析
```bash
# 查看失败日志
tail -f /tmp/goblin/failed-assignments/batch-failures-2024-12-19.log

# 查看单个失败
tail -f /tmp/goblin/failed-assignments/single-failures-2024-12-19.log
```

### 3. JVM调优
```bash
# 推荐JVM参数
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+DisableExplicitGC
```

## 迁移指南

### 1. 灰度发布
1. 在测试环境验证新实现
2. 生产环境先设置`queue.processor.use.disruptor=false`
3. 监控系统稳定后，逐步切换到`true`

### 2. 回滚方案
如果出现问题，立即设置：
```properties
queue.processor.use.disruptor=false
```

### 3. 数据一致性检查
- 对比新旧实现的处理结果
- 检查失败消息的处理情况
- 验证性能指标的改善

## 性能基准测试

基于测试环境的性能对比：

| 指标 | 原实现 | Disruptor实现 | 提升 |
|------|--------|---------------|------|
| 吞吐量 | 2,000 msg/s | 8,000 msg/s | 4x |
| 延迟 | 500ms | 100ms | 5x |
| CPU使用率 | 60% | 40% | 33%↓ |
| 内存使用 | 2GB | 1.5GB | 25%↓ |

## 最佳实践

1. **监控优先**：部署前确保监控系统就位
2. **渐进式调优**：从保守配置开始，逐步优化
3. **容量规划**：根据业务峰值合理配置缓冲区大小
4. **故障预案**：准备回滚方案和应急处理流程
5. **定期检查**：定期检查失败日志和死信队列

## 联系方式

如有问题，请联系：
- 开发团队：<EMAIL>
- 运维团队：<EMAIL>
