package com.wacai.loan.goblin.service.strategy.core.support;

import com.wacai.loan.goblin.common.constant.AssignCollectionType;
import com.wacai.loan.goblin.service.strategy.core.CandidateValueObtainer;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/20 上午11:03.
 */
@Component
public class AssignCollectionTypeCandidateValueObtainer implements CandidateValueObtainer {
    @Override
    public List<Map<String, String>> obtainCandidateValues() {
        List<Map<String, String>> candidateValues = new ArrayList<>();
        for(AssignCollectionType collectionType : AssignCollectionType.values()){
            Map<String, String> candidateValue = new HashMap<>();
            candidateValue.put("label",collectionType.getDesc());
            candidateValue.put("value",collectionType.name());
            candidateValues.add(candidateValue);
        }
        return candidateValues;
    }
}
