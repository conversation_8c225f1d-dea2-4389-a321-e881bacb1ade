package com.wacai.loan.goblin.service.assign.consumer;

import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 队列处理消费者配置类
 * 
 * 支持通过配置切换使用原有的QueueProcessConsumer或新的DisruptorQueueProcessConsumer
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Configuration
public class QueueProcessConsumerConfig {

    @Value("${kafka.queue.process.topic:loan.goblin.queue.process}")
    private String queueProcessTopic;

    @Value("${kafka.consumer.group:com.wacai.loan.goblin}")
    private String consumerGroup;

    @Value("${kafka.max.poll.records:500}")
    private int maxPollRecords;

    /**
     * 原有的队列处理消费者（默认）
     */
    @Bean
    @ConditionalOnProperty(name = "queue.processor.use.disruptor", havingValue = "false", matchIfMissing = true)
    public QueueProcessConsumer queueProcessConsumer() {
        log.info("Creating original QueueProcessConsumer");
        KafkaConsumerConfig config = createKafkaConsumerConfig();
        return new QueueProcessConsumer(config);
    }

    /**
     * 基于Disruptor的高性能队列处理消费者
     */
    @Bean
    @ConditionalOnProperty(name = "queue.processor.use.disruptor", havingValue = "true")
    public DisruptorQueueProcessConsumer disruptorQueueProcessConsumer() {
        log.info("Creating DisruptorQueueProcessConsumer");
        KafkaConsumerConfig config = createKafkaConsumerConfig();
        return new DisruptorQueueProcessConsumer(config);
    }

    private KafkaConsumerConfig createKafkaConsumerConfig() {
        KafkaConsumerConfig config = new KafkaConsumerConfig();
        config.setTopic(queueProcessTopic);
        config.setGroupId(consumerGroup);
        config.setMaxPollRecords(maxPollRecords);
        return config;
    }
}
