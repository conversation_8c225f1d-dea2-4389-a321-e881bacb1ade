package com.wacai.loan.goblin.service.loan.task;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.wacai.gbl.kafka.agent.producer.Producer;
import com.wacai.loan.goblin.common.util.DomainUtils;
import com.wacai.loan.goblin.loan.service.api.LoanService;
import com.wacai.loan.goblin.service.task.common.JobDomain;
import com.wacai.loan.tenant.spring.bean.context.ContextUtils;
import com.wacai.platform.prophet.client.annotations.JobDesc;
import com.wacai.platform.prophet.client.context.ExecuteContext;

import lombok.extern.slf4j.Slf4j;
/**
 * @description:同步案件
 * <AUTHOR>
 * @date 2022-06-02 14:46:49
 * @since JDK 1.8
 */
@Slf4j
@Component
@JobDesc(method = "doExecute", jobCode = "loan_goblin_job_loadLoanByLoanIdTask")
public class LoadLoanByAidTask {
    public static final int N_THREADS = 10;

    @Autowired
    private LoanService loanService;

    @Autowired
    private Producer producer;

    @Value("${loan.goblin.sync.loan.topic}")
    private String syncLoanTopic;

    @JobDomain
    public void doExecute(ExecuteContext executeContext) throws Throwable {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("doExecute start domain {}", ContextUtils.get());
        String param = executeContext.getJobParameters();
        JSONObject paramObj = JSON.parseObject(param);

        if (paramObj != null) {
            JSONArray idList = paramObj.getJSONArray("hermesAIdList");
            if (idList != null && !idList.isEmpty()) {
                List<String> list = idList.toJavaList(String.class);
                for (String appId : list) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("loanId",appId);
                    jsonObject.put("domain",ContextUtils.get());
                    producer.produce(syncLoanTopic, jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
                }
            }
            Long startloanId = paramObj.getLong("startloanId");
            Long endloanId = paramObj.getLong("endloanId");
            if (startloanId != null && endloanId != null) {
                for (; startloanId <= endloanId; startloanId++) {
                    Map<String, byte[]> mapFromEnv = DomainUtils.getMapFromEnv();
                    log.info("同步任务开始,发送hermes，{},header is {}",startloanId,mapFromEnv);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("loanId",startloanId);
                    jsonObject.put("domain",ContextUtils.get());
                    producer.produce(syncLoanTopic, jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
                }
            }
            Long startAll = paramObj.getLong("startAll");
            Long endAll = paramObj.getLong("endAll");
            if(startAll!=null && endAll!=null) {
                log.info("开始同步全量loanId!");
                Long startId = 0L;
                Integer limit = 5000;
                while(true) {
                    List<Long> loanIds = loanService.findLoanIdsByStartIdAndLimit(startId, limit);
                    if(CollectionUtils.isEmpty(loanIds)) {
                        break;
                    }
                    startId = loanIds.get(loanIds.size() -1);
                    Map<String, byte[]> mapFromEnv = DomainUtils.getMapFromEnv();
                    log.info("当前批次loanIds总量:{},lastId is {}, header is {}",loanIds.size(),startId,mapFromEnv);
                    for(Long loanId : loanIds) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("loanId",loanId);
                        jsonObject.put("domain",ContextUtils.get());
                        producer.produce(syncLoanTopic, jsonObject.toJSONString().getBytes(StandardCharsets.UTF_8));
                    }
                }
            }
        }

        log.info("doExecute end, takes {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

}