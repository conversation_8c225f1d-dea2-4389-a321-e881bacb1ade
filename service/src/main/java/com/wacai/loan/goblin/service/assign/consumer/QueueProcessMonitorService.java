package com.wacai.loan.goblin.service.assign.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 队列处理监控服务
 * 
 * 通过定时任务监控队列处理性能，采用日志方式输出监控信息
 * 监控频率设计：
 * - 性能监控：每5分钟
 * - 健康检查：每10分钟  
 * - 告警检查：每30分钟
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Service
@EnableScheduling
@ConditionalOnProperty(name = "queue.processor.monitor.enabled", havingValue = "true", matchIfMissing = true)
public class QueueProcessMonitorService {

    @Autowired(required = false)
    private DisruptorQueueProcessConsumer disruptorConsumer;

    @Autowired(required = false)
    private QueueProcessConsumer originalConsumer;

    @Value("${queue.processor.monitor.log.level:INFO}")
    private String logLevel;

    // 监控数据缓存，用于计算增量
    private final AtomicLong lastPublishedCount = new AtomicLong(0);
    private final AtomicLong lastProcessedCount = new AtomicLong(0);
    private final AtomicLong lastFailedCount = new AtomicLong(0);
    private volatile long lastMonitorTime = System.currentTimeMillis();
    
    private final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @PostConstruct
    public void init() {
        String consumerType = getConsumerType();
        log.info("Queue Process Monitor Service initialized - Consumer: {}, Log Level: {}", consumerType, logLevel);
    }

    /**
     * 性能监控 - 每5分钟执行一次
     * 记录处理速率、成功率等关键指标
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void performanceMonitor() {
        try {
            if (disruptorConsumer != null) {
                monitorDisruptorPerformance();
            } else if (originalConsumer != null) {
                monitorOriginalConsumerPerformance();
            }
        } catch (Exception e) {
            log.error("Error during performance monitoring", e);
        }
    }

    /**
     * 健康检查 - 每10分钟执行一次
     * 检查系统整体健康状态
     */
    @Scheduled(fixedRate = 600000) // 10分钟
    public void healthCheck() {
        try {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            HealthStatus health = calculateHealthStatus();
            
            if (health.isHealthy()) {
                if ("DEBUG".equals(logLevel)) {
                    log.info("[{}] Health Check: {} - {}", timestamp, health.getStatus(), health.getMessage());
                }
            } else {
                log.warn("[{}] Health Check: {} - {}", timestamp, health.getStatus(), health.getMessage());
            }
        } catch (Exception e) {
            log.error("Error during health check", e);
        }
    }

    /**
     * 告警检查 - 每30分钟执行一次
     * 检查异常情况并发送告警
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void alertCheck() {
        try {
            checkAndSendAlerts();
        } catch (Exception e) {
            log.error("Error during alert check", e);
        }
    }

    private void monitorDisruptorPerformance() {
        Map<String, Object> stats = disruptorConsumer.getStatistics();
        long currentTime = System.currentTimeMillis();
        String timestamp = LocalDateTime.now().format(timeFormatter);
        
        long currentPublished = (Long) stats.getOrDefault("publishedCount", 0L);
        long currentProcessed = (Long) stats.getOrDefault("processedCount", 0L);
        long currentFailed = (Long) stats.getOrDefault("failedCount", 0L);
        
        // 计算增量
        long publishedDelta = currentPublished - lastPublishedCount.get();
        long processedDelta = currentProcessed - lastProcessedCount.get();
        long failedDelta = currentFailed - lastFailedCount.get();
        
        // 计算时间间隔和速率
        double timeIntervalSeconds = (currentTime - lastMonitorTime) / 1000.0;
        double processRate = timeIntervalSeconds > 0 ? processedDelta / timeIntervalSeconds : 0;
        
        // 计算成功率和缓冲区使用率
        double successRate = currentPublished > 0 ? (double) currentProcessed / currentPublished * 100 : 100.0;
        double bufferUtilization = (Double) stats.getOrDefault("bufferUtilization", 0.0) * 100;
        
        // 根据日志级别和活动情况决定是否输出
        boolean hasActivity = publishedDelta > 0 || processedDelta > 0 || failedDelta > 0;
        
        if ("DEBUG".equals(logLevel)) {
            log.info("[{}] DisruptorConsumer Performance - " +
                    "Total: P:{} Pr:{} F:{}, " +
                    "Delta: +{} (+{} +{}), " +
                    "Rate: {:.1f}/s, " +
                    "Success: {:.2f}%, " +
                    "Buffer: {:.1f}%",
                    timestamp, currentPublished, currentProcessed, currentFailed,
                    publishedDelta, processedDelta, failedDelta,
                    processRate, successRate, bufferUtilization);
        } else if ("INFO".equals(logLevel) && hasActivity) {
            log.info("[{}] DisruptorConsumer - Processed: +{} ({:.1f}/s), Failed: +{}, Success: {:.2f}%, Buffer: {:.1f}%",
                    timestamp, processedDelta, processRate, failedDelta, successRate, bufferUtilization);
        }
        
        // 更新缓存值
        lastPublishedCount.set(currentPublished);
        lastProcessedCount.set(currentProcessed);
        lastFailedCount.set(currentFailed);
        lastMonitorTime = currentTime;
    }

    private void monitorOriginalConsumerPerformance() {
        String timestamp = LocalDateTime.now().format(timeFormatter);
        
        if ("DEBUG".equals(logLevel)) {
            log.info("[{}] OriginalConsumer - Active (detailed metrics not available)", timestamp);
        }
    }

    private HealthStatus calculateHealthStatus() {
        if (disruptorConsumer != null) {
            Map<String, Object> stats = disruptorConsumer.getStatistics();
            
            long published = (Long) stats.getOrDefault("publishedCount", 0L);
            long processed = (Long) stats.getOrDefault("processedCount", 0L);
            long failed = (Long) stats.getOrDefault("failedCount", 0L);
            
            double failureRate = published > 0 ? (double) failed / published : 0.0;
            double bufferUtilization = (Double) stats.getOrDefault("bufferUtilization", 0.0);
            
            if (failureRate > 0.2) {
                return new HealthStatus("CRITICAL", false, 
                        String.format("Very high failure rate: %.2f%%", failureRate * 100));
            } else if (failureRate > 0.1) {
                return new HealthStatus("DEGRADED", false, 
                        String.format("High failure rate: %.2f%%", failureRate * 100));
            } else if (bufferUtilization > 0.8) {
                return new HealthStatus("DEGRADED", false, 
                        String.format("High buffer utilization: %.1f%%", bufferUtilization * 100));
            } else {
                return new HealthStatus("HEALTHY", true, 
                        String.format("Success rate: %.2f%%, Buffer: %.1f%%", 
                                (1 - failureRate) * 100, bufferUtilization * 100));
            }
        } else if (originalConsumer != null) {
            return new HealthStatus("HEALTHY", true, "Original consumer is running");
        } else {
            return new HealthStatus("DOWN", false, "No queue process consumer found");
        }
    }

    private void checkAndSendAlerts() {
        if (disruptorConsumer == null) {
            return;
        }

        Map<String, Object> stats = disruptorConsumer.getStatistics();
        String timestamp = LocalDateTime.now().format(timeFormatter);
        
        long published = (Long) stats.getOrDefault("publishedCount", 0L);
        long processed = (Long) stats.getOrDefault("processedCount", 0L);
        long failed = (Long) stats.getOrDefault("failedCount", 0L);
        
        double failureRate = published > 0 ? (double) failed / published : 0.0;
        double bufferUtilization = (Double) stats.getOrDefault("bufferUtilization", 0.0);
        
        // 告警检查
        if (failureRate > 0.2) {
            log.error("[{}] CRITICAL ALERT - Very high failure rate: {:.2f}% (Total: {}, Failed: {})", 
                    timestamp, failureRate * 100, published, failed);
        } else if (failureRate > 0.1) {
            log.warn("[{}] WARNING ALERT - High failure rate: {:.2f}% (Total: {}, Failed: {})", 
                    timestamp, failureRate * 100, published, failed);
        }
        
        if (bufferUtilization > 0.8) {
            log.warn("[{}] WARNING ALERT - High buffer utilization: {:.1f}% - Consider scaling up", 
                    timestamp, bufferUtilization * 100);
        }
        
        // 定期性能摘要（每30分钟）
        if (published > 0) {
            long uptimeMinutes = (System.currentTimeMillis() - lastMonitorTime) / 60000;
            double avgRate = uptimeMinutes > 0 ? (double) processed / uptimeMinutes : 0;
            
            log.info("[{}] Performance Summary (30min) - " +
                    "Total Processed: {}, Avg Rate: {:.1f}/min, Success Rate: {:.2f}%, Buffer Avg: {:.1f}%", 
                    timestamp, processed, avgRate, (1 - failureRate) * 100, bufferUtilization * 100);
        }
    }

    private String getConsumerType() {
        if (disruptorConsumer != null) {
            return "DisruptorQueueProcessConsumer";
        } else if (originalConsumer != null) {
            return "QueueProcessConsumer";
        } else {
            return "None";
        }
    }

    /**
     * 健康状态数据结构
     */
    private static class HealthStatus {
        private final String status;
        private final boolean healthy;
        private final String message;

        public HealthStatus(String status, boolean healthy, String message) {
            this.status = status;
            this.healthy = healthy;
            this.message = message;
        }

        public String getStatus() { return status; }
        public boolean isHealthy() { return healthy; }
        public String getMessage() { return message; }
    }
}
