package com.wacai.loan.goblin.service.ivrcall.core.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wacai.gbl.jasmine.annotation.JasmineValue;
import com.wacai.gbl.middleware.redis.GblRedisClient;
import com.wacai.loan.goblin.common.config.jasmine.JasmineConfig;
import com.wacai.loan.goblin.common.constant.VoiceTemplet;
import com.wacai.loan.goblin.domain.DomainUtils;
import com.wacai.loan.goblin.linkup.LinkupAPI;
import com.wacai.loan.goblin.linkup.exception.LinkupException;
import com.wacai.loan.goblin.linkup.model.AutoCallRequest;
import com.wacai.loan.goblin.loan.customer.api.CustomerService;
import com.wacai.loan.goblin.loan.customer.api.dto.CustomerDTO;
import com.wacai.loan.goblin.loan.service.api.LoanService;
import com.wacai.loan.goblin.loan.service.api.dto.LoanDTO;
import com.wacai.loan.goblin.service.clean.api.dto.MobileManagerDTO;
import com.wacai.loan.goblin.service.collection.api.CollectionService;
import com.wacai.loan.goblin.service.collection.api.dto.CollectionDTO;
import com.wacai.loan.goblin.service.common.httpservice.api.HttpClient;
import com.wacai.loan.goblin.service.common.httpservice.api.dto.HttpRequestResult;
import com.wacai.loan.goblin.service.ivrcall.api.dto.AutoCallDTO;
import com.wacai.loan.goblin.service.ivrcall.api.dto.AutoCallRequestMessageDTO;
import com.wacai.loan.goblin.service.ivrcall.core.util.IVRCallUtil;
import com.wacai.loan.tenant.spring.bean.context.ContextUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanMetadataAttribute;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2017/7/26
 */
@Slf4j
@Service
public class AutoCallServiceSupport {

    public static final String CREATE = "create";
    public static final String PULL = "pull";
    public static final String PULL_NUMBER_CLEAN = "pullNumberClean";
    public static final String LIST_NUMBER_CLEAN_RESULT = "listNumberCleanResult";

    public static final String INSERT_NUMBER_CLEAN = "insertNumberClean";
    public static final String EXIST = "1";
    public static final long SUCCESS = 1L;
    @Autowired
    private HttpClient httpClient;
    @Autowired
    private LoanService loanService;
    @Autowired
    private CollectionService collectionService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private GblRedisClient redisCluster;

    @Value("${autocall.url.prefix}")
    private String autocallPrefix;
    @Value("${autoCalls.url.suffix}")
    private String autocallSuffix;
    @Value("${autoCalls.br.url.suffix}")
    private String autocallBrSuffix;

    @Value("${auto_call.url}")
    private String ivrRecordsUrl;
    @Value("${auto_call_insert:insert}")
    private String insert;
    @Value("${auto_call_get:get}")
    private String get;
    @Value("${auto_call_daily_from_first:09:00:00}")
    private String dailyFromOfFirst;
    @Value("${auto_call_daily_from_second:10:00:00}")
    private String dailyFromOfSecond;
    @Value("${auto_call_daily_till:17:00:00}")
    private String dailyTill;
    @Value("${auto_call_attempt:0}")
    private String attempt;
    @Value("${last_four_debit_card_no:4}")
    private int lastFourDebitCardNo;
    //@JasmineValue(key = "ivrcall.autoCall.prefixs", subModule = JasmineConfig.GOBLIN_SERVICE, probe = true)
    private String prefixs = "9123,9124,9048,9049";
    @Value("${auto.call.update:updateByBusinessKey}")
    private String updateByBusinessKey;

  //  @JasmineValue(key = "auto.call.cache.expired.time", defaultValue = "36000", subModule = JasmineConfig.GOBLIN_SERVICE, probe = true)
    private int mobileCacheExpiredTime = 36000;

   // @JasmineValue(key = "auto.call.br.suffix.campaign", defaultValue = "", subModule = JasmineConfig.GOBLIN_SERVICE, probe = true, notEmpty = false )
    private String brCampaigns = "5e49f506c2d00a12d90377dc";

    @Autowired
    private LinkupAPI linkupAPI;

    public void pushAutoCallFromCleanMobile(List<MobileManagerDTO> mobileManagerDTOList){
        if (CollectionUtils.isEmpty(mobileManagerDTOList)) {
            return;
        }
        List<MobileManagerDTO> filterMobileManagerDTO = mobileManagerDTOList.stream()
                .filter(mobileManager -> filterMobileManagerDTO(mobileManager.getMobile())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterMobileManagerDTO)) {
            return;
        }
        JSONArray values = fillPushObjectFromCleanMobile(filterMobileManagerDTO);
        JSONObject pushObject = new JSONObject();
        pushObject.put("values",values);
        httpClient.post(ivrRecordsUrl + INSERT_NUMBER_CLEAN, (JSON) pushObject);
        log.info("push [{}] clean mobile to ivr server by auto call", values.size());
    }

    private boolean filterMobileManagerDTO(String mobile) {
        try {
            long result = redisCluster.setnx(DomainUtils.getDomainKey(mobile), EXIST);
            if (SUCCESS == result) {
                redisCluster.expire(DomainUtils.getDomainKey(mobile), mobileCacheExpiredTime);
                return true;
            }
            return false;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private JSONArray fillPushObjectFromCleanMobile(List<MobileManagerDTO> mobileManagerDTOList) {
        JSONArray values = new JSONArray();
        for (MobileManagerDTO mobileManagerDTO : mobileManagerDTOList){
            AutoCallPushObject autoCallPushObject = new AutoCallPushObject();
            autoCallPushObject.setPhone(mobileManagerDTO.getMobile());
            autoCallPushObject.setIvrValue(getIvrValueFromCleanMobile(mobileManagerDTO.getCollectionId()));
            JSONObject userData = new JSONObject();
            userData.put("domain", ContextUtils.get());
            autoCallPushObject.setUserData(userData.toString());
            values.add(autoCallPushObject);
        }
        return values;
    }

    public JSONArray pullAutoCallList(JSONArray businessKeyArr){

        if (CollectionUtils.isEmpty(businessKeyArr)) {
            return null;
        }
        log.info("begin pull auto call from genesys");
        JSONObject pullObject = new JSONObject();
        pullObject.put("businessKeyList", businessKeyArr);

        JSONObject pullContentObject = httpClient.post(ivrRecordsUrl + get, (JSON) pullObject).asJSONObject();

        if (pullContentObject == null) {
            log.info("pull auto call failed");
            return null;
        }
        log.info("end pull auto call from genesys");
        JSONArray pullAutoCallArr = pullContentObject.getJSONObject("result").getJSONArray("list");

        return pullAutoCallArr;
    }

    public JSONArray pullAutoCallFromPredictCall(JSONArray businessKeyArr){
        if (CollectionUtils.isEmpty(businessKeyArr)) {
            return null;
        }

        log.info("begin pull auto call from predict call");
        JSONObject pullObject = new JSONObject();
        pullObject.put("businessKeyList", businessKeyArr);

        JSONObject pullContentObject = httpClient.post(ivrRecordsUrl + PULL, (JSON) pullObject).asJSONObject();

        if (pullContentObject == null) {
            log.info("pull auto call failed");
            return null;
        }
        log.info("end pull auto call from predict call");
        JSONArray pullAutoCallArr = pullContentObject.getJSONObject("result").getJSONArray("list");
        return pullAutoCallArr;
    }

    public JSONArray pullAutoCallFromCleanMobile(JSONArray mobileArray){
        if (CollectionUtils.isEmpty(mobileArray)) {
            return null;
        }
        log.info("begin pull auto call from clean mobile");
        JSONObject pullObject = new JSONObject();
        pullObject.put("businessKeyList", mobileArray);

        HttpRequestResult httpRequestResult = httpClient.post(ivrRecordsUrl + LIST_NUMBER_CLEAN_RESULT, (JSON) pullObject);
        JSONArray pullAutoCallArr = new JSONArray();
        if (!httpRequestResult.isOk()) {
            log.info("pull clean mobile result from ivr server error");
            return pullAutoCallArr;
        }

        JSONObject pullContentObject = httpRequestResult.asJSONObject();
        if (pullContentObject == null) {
            log.info("pull auto call failed from clean mobile");
            return null;
        }
        log.info("end pull auto call from from clean mobile");
        pullAutoCallArr = pullContentObject.getJSONObject("result").getJSONArray("list");
        log.info("pullAutoCallArr : " + pullAutoCallArr.toJSONString());
        return pullAutoCallArr;
    }

    public int updateByBusinessKey(String businessKey) {
        int result =0;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("businessKey", businessKey);

        try {
            JSONObject pullObject = httpClient.post(ivrRecordsUrl + updateByBusinessKey, paramMap).asJSONObject();
            if (pullObject !=null && pullObject.getBooleanValue("success")) {
                result =1;
            }
        } catch (Exception e) {
           log.info("updateByBusinessKey error with businessKey [{}]", businessKey, e);
        }

        return result;
    }

    public String getIvrValue(AutoCallDTO autoCallDTO) {

        String recallDate = null;
        if (autoCallDTO.getRecallDate() != null) {
            recallDate = IVRCallUtil.formatDate(autoCallDTO.getRecallDate(), "yyyy-MM-dd");
        }
        String debitCardNo = autoCallDTO.getDebitCardno();
        String lastFourCardNo = null;

        if ( debitCardNo == null || debitCardNo.length() <= lastFourDebitCardNo ) {
            log.debug("debitCardNo is invalid for autoCall id :[{}]!", autoCallDTO.getId());
        } else {
            lastFourCardNo = debitCardNo.substring(debitCardNo.length() - lastFourDebitCardNo, debitCardNo.length());
        }

        String ivrvalue = StringUtils.join(new String[]{autoCallDTO.getRealname(), autoCallDTO.getGender()
                , autoCallDTO.getClient(), String.valueOf(autoCallDTO.getOverdueAmount()), recallDate, lastFourCardNo}, ",");
        return ivrvalue;
    }

    public String getIvrValueFromCleanMobile(Long collectionId) {
        CollectionDTO collectionDTO = collectionService.findById(collectionId);
        if (collectionDTO == null || collectionDTO.getLoanId() == null) {
            return null;
        }
        LoanDTO loanDTO = loanService.findById(collectionDTO.getLoanId());
        if (loanDTO == null || loanDTO.getCustomerId() == null) {
            return null;
        }
        CustomerDTO customerDTO = customerService.getCustomerById(loanDTO.getCustomerId());

        if (customerDTO == null) {
            return null;
        }

        return customerDTO.getRealName();
    }


    @Getter
    @Setter
    private class AutoCallPushObject {
        private String businessKey;
        private String prefix;
        private String attempt;
        private String ivrValue;
        private String phone;
        private String ivrName;
        private String dailyFrom;
        private String dailyTill;
        private String prefixTag;
        private String userData;
    }

    public void sendAutoCallMessage(AutoCallRequestMessageDTO autoCallRequestMessage) {
        //自动外呼kafka发送改为http请求
        log.info("send auto call message :{}", autoCallRequestMessage);
        AutoCallRequest autoCallRequest = new AutoCallRequest();
        try {
            BeanUtils.copyProperties(autoCallRequestMessage,autoCallRequest);
            if(autoCallRequestMessage.getRecordData() != null){
                autoCallRequest.setRecordData(JSON.parseObject(JSON.toJSONString(autoCallRequestMessage.getRecordData()),Map.class));
            }
            linkupAPI.pushAutoCall(autoCallRequest);
        } catch (LinkupException e) {
            log.error("LinkupException",e);
        }
    }

    private boolean isBrCampaign(String campaignKey) {
        try {
            String[] brCampaignArray = brCampaigns.split(",");
            List<String> brCampaignList = Arrays.stream(brCampaignArray).map(s -> s.trim()).collect(Collectors.toList());
            return brCampaignList.contains(campaignKey);
        } catch (Exception e) {
            log.error("br keys {} campaign key {} error", brCampaigns, campaignKey);
            return false;
        }
    }

    @Data
    public class CallingListInsertRequest implements Serializable {
        private List<AutoCallRequestMessageDTO> values;
    }
}
