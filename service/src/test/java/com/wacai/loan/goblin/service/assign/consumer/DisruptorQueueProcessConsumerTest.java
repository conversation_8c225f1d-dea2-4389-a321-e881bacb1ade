package com.wacai.loan.goblin.service.assign.consumer;

import com.alibaba.fastjson.JSON;
import com.wacai.gbl.kafka.agent.consumer.KafkaConsumerConfig;
import com.wacai.gbl.kafka.agent.consumer.Message;
import com.wacai.loan.goblin.service.assign.api.QueueProcessorMap;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * DisruptorQueueProcessConsumer性能和功能测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DisruptorQueueProcessConsumerTest {

    @Mock
    private QueueProcessorMap queueProcessorMap;

    @Mock
    private FailureHandlingService failureHandlingService;

    private DisruptorQueueProcessConsumer consumer;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        
        KafkaConsumerConfig config = new KafkaConsumerConfig();
        config.setTopic("test-topic");
        config.setGroupId("test-group");
        
        consumer = new DisruptorQueueProcessConsumer(config);
        ReflectionTestUtils.setField(consumer, "queueProcessorMap", queueProcessorMap);
        ReflectionTestUtils.setField(consumer, "failureHandlingService", failureHandlingService);
        ReflectionTestUtils.setField(consumer, "consumerThreadNum", 4);
        ReflectionTestUtils.setField(consumer, "bufferSize", 1024);
        ReflectionTestUtils.setField(consumer, "batchSize", 100);
        ReflectionTestUtils.setField(consumer, "batchTimeoutMs", 50L);
        
        consumer.afterPropertiesSet();
    }

    @Test
    void testBasicMessageProcessing() throws InterruptedException {
        // 准备测试数据
        AssignmentDTO dto = createTestAssignmentDTO(1L, "TEST_QUEUE", 100L);
        Message message = createTestMessage(dto);

        CountDownLatch latch = new CountDownLatch(1);
        
        // 模拟处理成功
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(queueProcessorMap).streamingProcessQueue(any(List.class));

        // 发送消息
        consumer.onMessageReceived(message);

        // 等待处理完成
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Message should be processed within 5 seconds");
        
        // 验证调用
        verify(queueProcessorMap, timeout(1000)).streamingProcessQueue(any(List.class));
    }

    @Test
    void testBatchProcessing() throws InterruptedException {
        int messageCount = 150; // 超过批大小100
        CountDownLatch latch = new CountDownLatch(2); // 期望至少2个批次

        doAnswer(invocation -> {
            List<AssignmentDTO> batch = invocation.getArgument(0);
            System.out.println("Processing batch of size: " + batch.size());
            latch.countDown();
            return null;
        }).when(queueProcessorMap).streamingProcessQueue(any(List.class));

        // 发送多条消息
        for (int i = 0; i < messageCount; i++) {
            AssignmentDTO dto = createTestAssignmentDTO((long) i, "TEST_QUEUE", 100L + i);
            Message message = createTestMessage(dto);
            consumer.onMessageReceived(message);
        }

        // 等待批处理完成
        assertTrue(latch.await(10, TimeUnit.SECONDS), "Batch processing should complete within 10 seconds");
    }

    @Test
    void testFailureHandling() throws InterruptedException {
        AssignmentDTO dto = createTestAssignmentDTO(1L, "TEST_QUEUE", 100L);
        Message message = createTestMessage(dto);

        CountDownLatch latch = new CountDownLatch(1);

        // 模拟处理失败
        doThrow(new RuntimeException("Test exception"))
                .when(queueProcessorMap).streamingProcessQueue(any(List.class));

        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(failureHandlingService).handleBatchFailure(any(List.class), any(Exception.class), anyString());

        // 发送消息
        consumer.onMessageReceived(message);

        // 等待失败处理
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Failure handling should be called within 5 seconds");
        
        // 验证失败处理被调用
        verify(failureHandlingService, timeout(2000)).handleBatchFailure(any(List.class), any(Exception.class), anyString());
    }

    @Test
    void testPerformance() throws InterruptedException {
        int messageCount = 10000;
        AtomicInteger processedCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(messageCount);

        doAnswer(invocation -> {
            List<AssignmentDTO> batch = invocation.getArgument(0);
            processedCount.addAndGet(batch.size());
            for (int i = 0; i < batch.size(); i++) {
                latch.countDown();
            }
            return null;
        }).when(queueProcessorMap).streamingProcessQueue(any(List.class));

        long startTime = System.currentTimeMillis();

        // 发送大量消息
        for (int i = 0; i < messageCount; i++) {
            AssignmentDTO dto = createTestAssignmentDTO((long) i, "TEST_QUEUE", 100L + i);
            Message message = createTestMessage(dto);
            consumer.onMessageReceived(message);
        }

        // 等待所有消息处理完成
        assertTrue(latch.await(30, TimeUnit.SECONDS), "All messages should be processed within 30 seconds");

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double throughput = (double) messageCount / duration * 1000; // messages per second

        System.out.printf("Performance Test Results:\n");
        System.out.printf("Messages: %d\n", messageCount);
        System.out.printf("Duration: %d ms\n", duration);
        System.out.printf("Throughput: %.2f messages/second\n", throughput);
        System.out.printf("Processed: %d\n", processedCount.get());

        assertEquals(messageCount, processedCount.get(), "All messages should be processed");
        assertTrue(throughput > 1000, "Throughput should be greater than 1000 messages/second");
    }

    @Test
    void testStatistics() {
        var stats = consumer.getStatistics();
        
        assertNotNull(stats);
        assertTrue(stats.containsKey("publishedCount"));
        assertTrue(stats.containsKey("processedCount"));
        assertTrue(stats.containsKey("failedCount"));
        assertTrue(stats.containsKey("bufferSize"));
        assertTrue(stats.containsKey("consumerThreadNum"));
        assertTrue(stats.containsKey("batchSize"));
    }

    private AssignmentDTO createTestAssignmentDTO(Long collectionId, String queue, Long strategyId) {
        AssignmentDTO dto = new AssignmentDTO();
        dto.setCollectionId(collectionId);
        dto.setQueue(queue);
        dto.setStrategyId(strategyId);
        return dto;
    }

    private Message createTestMessage(AssignmentDTO dto) {
        Message message = new Message();
        message.setValue(JSON.toJSONString(dto).getBytes(StandardCharsets.UTF_8));
        message.setOffset(System.currentTimeMillis());
        return message;
    }
}
