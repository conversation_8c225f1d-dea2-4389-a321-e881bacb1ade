<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
        http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>goblin-service-core</artifactId>
    <name>Goblin Service Core</name>
    <description>Goblin Service Core</description>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.wacai.loan</groupId>
        <artifactId>goblin</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../</relativePath>
    </parent>

    <dependencies>
        <!-- job -->
        <dependency>
            <groupId>com.wacai.platform</groupId>
            <artifactId>prophet-spring</artifactId>
        </dependency>
        <!-- graphql -->
        <dependency>
            <groupId>org.cokebook.graphql</groupId>
            <artifactId>graphql-scalars</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>graphql-spring</artifactId>
                    <groupId>org.cokebook.graphql</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.cokebook.graphql</groupId>
            <artifactId>graphql-spring</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.graphql-java</groupId>-->
<!--            <artifactId>graphql-spring-boot-starter</artifactId>-->
<!--            <version>4.0.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.graphql-java</groupId>-->
<!--            <artifactId>graphql-java-tools</artifactId>-->
<!--            <version>4.3.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-dubbo-cfg</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.wacai.loan</groupId>-->
<!--            <artifactId>medivh-client</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>medivh-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-traffic-router</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.tigon</groupId>
            <artifactId>tigon-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>loan-photo-service-api</artifactId>
            <version>0.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>loan-application-api</artifactId>
            <version>1.3.15-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>spring-boot-starter-elastic-rest</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.wacai</groupId>-->
<!--            <artifactId>kafka-http-client</artifactId>-->
<!--            <version>2.1.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>gbl-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>io.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.wacai.loan</groupId>-->
<!--            <artifactId>malone-api</artifactId>-->
<!--            <version>0.0.3-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-datasource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.6</version>
        </dependency>

        <!-- Provided Dependencies -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>mirana-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-domain-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-lock</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-lock</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!-- 资管图片上传 -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>file-storage-client</artifactId>
		</dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-statistics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>linkup</artifactId>
        </dependency>
        
        <!-- 新告警平台 -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>redalert-client</artifactId>
		</dependency>

        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-query-api</artifactId>
        </dependency>


        <dependency>
            <artifactId>gbl-file-storage</artifactId>
            <groupId>com.wacai.loan</groupId>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>
    </dependencies>
    <build>
    </build>
</project>
