package com.wacai.loan.goblin.common.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum AppFlow {

	REPEAT(1,"续贷"), NO_CARD(2,"无银贷"),ONE_INSTALLMENT(3,"1000元一期"),NEW_REPEAT(4,"续贷");
	private Integer flow;
	private String flowName;


	static Map<Integer, AppFlow> appFlowMap = new HashMap<Integer, AppFlow>();
	static List<AppFlow> appFlowList = new ArrayList<AppFlow>();

	static {
		AppFlow[] appFlows = values();
		for (AppFlow appFlow : appFlows) {
			appFlowMap.put(appFlow.getFlow(), appFlow);
			appFlowList.add(appFlow);
		}
	}
	private AppFlow(Integer flow, String flowName){
		this.flow = flow;
		this.flowName = flowName;
	}
	public Integer getFlow() {
		return flow;
	}
	public void setFlow(Integer flow) {
		this.flow = flow;
	}
	public String getFlowName() {
		return flowName;
	}
	public void setFlowName(String flowName) {
		this.flowName = flowName;
	}

	public static String getNameByFlow(int flow){
		AppFlow appFlow = appFlowMap.get(flow);
		if(appFlow == null){
			return null;
		}
		return appFlow.getFlowName();
	}

	public static List<AppFlow> getAppFlowList() {
		return appFlowList;
	}
}
