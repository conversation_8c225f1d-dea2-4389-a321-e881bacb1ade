package com.wacai.loan.goblin.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018/5/15
 */
@Slf4j
public abstract class ConvertUtil {
    public static final String DAY_PATTERN = "yyyy-mm-dd";
    public static final String PATTERN = "yyyy-MM-dd";
    public static final String PATTERN_WITH_TIME = "yyyy-MM-dd HH:mm:ss";
    public static final String SPACE = " ";

    public static <T> T convert(Object source, Class<T> cls) {
        if (!Optional.ofNullable(source).isPresent() || !Optional.ofNullable(cls).isPresent()) {
            return null;
        }

        try {
            T target = cls.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception ignored) {
            log.error("convert {} to {} failed", source, cls);
        }
        return null;
    }

    public static Date strToDate(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }

        String format = date.contains(SPACE) ? PATTERN_WITH_TIME : PATTERN;

        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            return null;
        }
    }
}
