package com.wacai.loan.goblin.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2019/1/3 下午4:50.
 */
@Slf4j
public class ManageableTaskExecutor {
    private String taskName;

    private int threadPoolSize;

    private final static int maxThreadPoolSize = 8;

    private final static int mixThreadPoolSize = 1;

    private Queue<Runnable> runnerList = new LinkedList<Runnable>();

    private volatile boolean isStarted = false;

    private ExecutorService fixedThreadPool;

    public ManageableTaskExecutor(String taskName, int threadPoolSize){
        Assert.isTrue(StringUtils.isNotEmpty(taskName),"taskName can not be empty!");
        Assert.isTrue(threadPoolSize <= maxThreadPoolSize && threadPoolSize >= mixThreadPoolSize,"threadPoolSize is illegal!");
        this.taskName = taskName;
        this.threadPoolSize = threadPoolSize;
    }

    public void addRunner(Runnable runnable){
        Assert.isTrue(!isStarted, "monitor started, can not add runner!");
        runnerList.add(runnable);
    }

    public void startAndAwaitTermination(){
        start();
        /**等待线程池中任务处理完*/
        fixedThreadPool.shutdown();
        while (!fixedThreadPool.isTerminated()){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("ThreadPoolProgressMonitor等待线程结束时被中断", e);
                break;
            }
        }
        log.info("end task [{}]",taskName);
    }

    public void start(){
        Assert.isTrue(runnerList.size() > 0, "no runner in queue!");
        isStarted = true;
        Integer sum = runnerList.size();
        AtomicInteger complete = new AtomicInteger(0);
        fixedThreadPool = Executors.newFixedThreadPool(threadPoolSize);
        while (runnerList.size() > 0){
            Runnable runnable = runnerList.poll();
            fixedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try{
                        runnable.run();
                    }catch (Throwable e){
                        log.error("observableRunner error !",e);
                    }
                    complete.incrementAndGet();
                }
            });
        }
        Thread monitor = new Thread(){
            public void run(){
                while (isStarted && complete.get() != sum){
                    BigDecimal percent = new BigDecimal(complete.get()).multiply(new BigDecimal(100)).divide(new BigDecimal(sum),2,BigDecimal.ROUND_HALF_UP);
                    log.info("task [{}] executing, complete [{}] %",taskName, percent);
                    try {
                        TimeUnit.SECONDS.sleep(2L);
                    } catch (InterruptedException e) {
                    }
                }
                log.info("task [{}] executing, complete " + "100.00" + "%",taskName);
            }
        };
        monitor.start();
        fixedThreadPool.shutdown();
    }

    public void stop(){
        isStarted = false;
        fixedThreadPool.shutdownNow();
        try {
            fixedThreadPool.awaitTermination(60, TimeUnit.SECONDS);
            log.info("task [{}] has been stopped!");
        } catch (InterruptedException e) {
            log.error("线程池{}等待关闭时被中断", fixedThreadPool.toString(), e);
        }
    }

    /**
     * 超时等待线程池结束
     * @param timeout
     * @param unit
     * @return 线程池是否已经结束
     */
    public boolean awaitTermination(long timeout, TimeUnit unit){
        boolean isTermination = false;
        try {
            isTermination = fixedThreadPool.awaitTermination( timeout, unit);
        } catch (InterruptedException e) {
            log.error("线程池{}等待关闭时被中断", fixedThreadPool.toString(), e);
        }
        return isTermination;
    }

    /**
     * 超时等待线程池结束（等待2秒超时返回）
     * @return 线程池是否已经结束
     */
    public boolean awaitTermination(){
        return awaitTermination(2, TimeUnit.SECONDS);
    }

}
