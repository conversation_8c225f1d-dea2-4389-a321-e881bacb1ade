package com.wacai.loan.goblin.common.constant;

/**
 * 消息中心短信发送状态枚举类
 * Created by Weimao on 2017/6/27.
 */
public enum McSmsStatus {

    SUCCESS(2, "发送结果：成功"),
    FAIL(3, "发送结果：失败"),
    BLACK(4, "发送结果：黑名单");

    private int value;
    private String desc;

    McSmsStatus(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByValue(int value) {
        for (McSmsStatus element : values()) {
            if (element.getValue() == value) {
                return element.getDesc();
            }
        }
        return null;
    }

    public static String getResultByValue(int value) {
        if (value == 1) {
            return "发送成功";
        }
        return "发送失败";
    }

    public static String getResultTypeByValue(int value) {
        if (value == 1) {
            return "SUCCESS";
        }
        return "FAIL";
    }
}
