package com.wacai.loan.goblin.common.util;

import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;

import java.security.MessageDigest;

/**
 * <AUTHOR> <br>
 * <EMAIL> <br>
 * Apr 05, 2017 17:33:19
 */
public final class Sha512Utils {

    /**
     * @param value
     * @param salt
     * @return
     */
    public static String encode(String value, String salt) {
        MessageDigest digest = DigestUtils.getSha512Digest();
        digest.reset();
        digest.update(salt.getBytes());
        return Hex.encodeHexString(digest.digest(value.getBytes()));
    }
}
