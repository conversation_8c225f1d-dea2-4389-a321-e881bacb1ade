package com.wacai.loan.cache;

import java.util.List;

/**
 * <AUTHOR>
 * 2019-01-29
 */
public interface MessageConfirmService {

    /**
     * 获取未确认的消息
     *
     * @param topic    topic
     * @param duration 时间间隔/秒
     * @return 消息列表
     */
    List<String> getUnconfirmed(String topic, long duration);

    /**
     * 待确认消息
     *
     * @param topic   topic
     * @param message message
     */
    void toBeConfirmed(String topic, String message);

    /**
     * 确认消息
     *
     * @param topic   topic
     * @param message message
     */
    void confirm(String topic, String message);

}
