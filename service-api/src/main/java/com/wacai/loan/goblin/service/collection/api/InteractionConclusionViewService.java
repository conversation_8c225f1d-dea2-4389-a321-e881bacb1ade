package com.wacai.loan.goblin.service.collection.api;

import com.wacai.loan.goblin.service.collection.api.dto.InteractionConclusionViewDTO;
import com.wacai.loan.tenant.spring.bean.annotation.TenantInterface;
import com.wacai.loan.tenant.spring.bean.config.ProtocolConfig;

import java.util.List;

/**
 * 综合备注Service
 */
@TenantInterface(protocol = ProtocolConfig.DUBBO)
public interface InteractionConclusionViewService {
	List<InteractionConclusionViewDTO> findByConclusionType(String type);
}
