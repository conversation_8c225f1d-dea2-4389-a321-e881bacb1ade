package com.wacai.loan.goblin.service.collection.api;

import java.util.List;

import com.wacai.loan.goblin.common.constant.DisposeStatus;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentDTO;
import com.wacai.loan.goblin.service.collection.api.dto.AssignmentHistoryDTO;
import com.wacai.loan.tenant.spring.bean.annotation.TenantInterface;
import com.wacai.loan.tenant.spring.bean.config.ProtocolConfig;

/**
 * @description:催收分案历史接口类
 * <AUTHOR>
 * @date 2020-10-12 14:21:53
 * @since JDK 1.8
 */
@TenantInterface(protocol = ProtocolConfig.DUBBO)
public interface AssignmentHistoryService {

	/**
	 * getById:根据主键id查询催收案件分案历史记录
	 * @param id
	 * @return
	 * <AUTHOR>
	 * @date 2020-10-13 14:49:25
	 */
	AssignmentHistoryDTO getById(Long id);

	/**
	 * save:保存催收案件分案历史信息
	 * @param assignmentHistoryDTO
	 * @return
	 * <AUTHOR>
	 * @date 2020-10-13 14:50:48
	 */
	AssignmentHistoryDTO save(AssignmentHistoryDTO assignmentHistoryDTO);
	
	/**
	 * findByCollectionIdAndActiveOrderByCreatedTimeDesc:根据案件id按照创建时间倒序查询催收案件分案历史信息记录
	 * @param collectionId
	 * @return
	 * <AUTHOR>
	 * @date 2020-10-13 14:51:09
	 */
	List<AssignmentHistoryDTO> findByCollectionIdAndActiveOrderByCreatedTimeDesc(Long collectionId);

	/**
	 * 通过分案记录更新历史
	 * @param assignmentDTO
	 * @return
	 */
	void saveByAssignmentDTO(AssignmentDTO assignmentDTO);

	AssignmentHistoryDTO findLastByCollectionIdAndStatusAndActive(Long collectionId, List<String> statuses, boolean active);
}
