<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>goblin</artifactId>
        <groupId>com.wacai.loan</groupId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>goblin-amc-statistics-bootstrap</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-agent-cost-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.loan</groupId>
            <artifactId>goblin-amc-agent-cost-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wacai.tigon</groupId>
            <artifactId>tigon-webmvc-spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>test</id>
        </profile>
        <profile>
            <id>prod</id>
        </profile>
        <profile>
            <id>nexus</id>
        </profile>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
    </profiles>
</project>
