package com.wacai.loan.goblin.strategy;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@SpringBootApplication(exclude = { DataSourceTransactionManagerAutoConfiguration.class,
		DataSourceAutoConfiguration.class, },scanBasePackages = "com.wacai.loan.goblin.strategy")
@ImportResource({ "classpath:/spring/*.xml" })
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableScheduling
public class StrategyStart {
	public static void main(String[] args) {
		// parallelStream 默认线程数调整
		System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism",
				String.valueOf(Runtime.getRuntime().availableProcessors() * 3 / 2));
		SpringApplication.run(StrategyStart.class, args);
	}
}
