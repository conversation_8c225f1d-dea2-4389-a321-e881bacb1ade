package com.wacai.loan.goblin.strategy.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.PropertySource;

@Slf4j
@Configuration
public class DataSourceConfiguration {

	@PropertySource(value = {"classpath:application.properties"})
    @Profile(value = {"dev", "test", "staging", "production"})
    protected static class DevConfig extends DruidDataSource {

        @Value("${spring.profiles.active}")
        private String active;

        @Bean(initMethod = "init", destroyMethod = "close", name = "goblinDataSource")
        @ConfigurationProperties("goblin.datasource")
        @ConditionalOnProperty(name = "goblin.datasource.config", havingValue = "true", matchIfMissing = false)
        public DruidDataSource privateFundDataSource() {
            log.info("environment:{} goblinDataSource init.", active);
            return super.cloneDruidDataSource();
        }
    }

}
