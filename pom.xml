<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
        http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wacai.loan</groupId>
    <artifactId>goblin</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>Wacai Loan Goblin</name>
    <description>Wacai Loan Goblin</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <goblin.version>0.0.1-SNAPSHOT</goblin.version>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>utf-8</project.build.sourceEncoding>
        <jackson.version>2.9.4</jackson.version>
        <elasticsearch.version>5.6.16</elasticsearch.version>
        <spring-boot.version>2.0.0.RELEASE</spring-boot.version>
        <spring.version>5.0.4.RELEASE</spring.version>
        <spring.retry.version>1.1.2.RELEASE</spring.retry.version>

        <little.boy.api.version>3.5</little.boy.api.version>

        <mirana.version>0.0.4</mirana.version>

        <ninja.client.version>1.2.1-SNAPSHOT</ninja.client.version>

        <cache.starter.version>1.2.15</cache.starter.version>

        <medivh.api.version>0.0.12</medivh.api.version>

        <medivh.client.version>0.0.12</medivh.client.version>

        <sharding-sphere.version>3.0.0</sharding-sphere.version>

        <data-service.dubbo.version>1.0.7</data-service.dubbo.version>

        <orianna.version>0.0.1-SNAPSHOT</orianna.version>

        <redalert.client.version>1.1.1</redalert.client.version>

        <start-class></start-class>
    </properties>

    <modules>
        <module>library</module>
        <module>service-api</module>
        <module>service</module>
        <module>query-api</module>
        <module>query</module>
        <module>job</module>
        <module>web</module>
        <module>message-api</module>
        <module>loan-api</module>
        <module>transformer-message</module>
        <module>loan</module>
        <module>tenant-api</module>
        <module>tenant</module>
        <module>domain-config</module>
        <module>goblin-agent-statistic</module>
        <module>strategy</module>
        <module>service-bootstrap</module>
        <module>statistics-bootstrap</module>
        <module>goblin-statistics</module>
        <module>linkup</module>

    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-asset-statistics</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>mirana-api</artifactId>
                <version>${mirana.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-model</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-domain-config</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-datasource</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-mapper</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-jpa-repo</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-es-repo</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-http-client-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-tenant</artifactId>
                <version>0.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-tenant-api</artifactId>
                <version>0.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-http-client-apache</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-common</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-traffic-router</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-statistics</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-dubbo-cfg</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-jasmine</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-service-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-service-core</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-elastic-model</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-elastic-kafka-producer</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-message-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-query-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-query</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-loan-api</artifactId>
                <version>0.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-loan</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>linkup</artifactId>
                <version>${goblin.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.wacai.loan</groupId>-->
<!--                <artifactId>medivh-client</artifactId>-->
<!--                <version>${medivh.client.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>medivh-api</artifactId>
                <version>${medivh.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>spring-boot-starter-elastic-rest</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-agent-cost-config-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-agent-cost-config</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-agent-cost-core-api</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>goblin-amc-agent-cost-core</artifactId>
                <version>${goblin.version}</version>
            </dependency>

            <!-- Tigon -->
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-model</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-mybatis</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-sequence</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-service-support</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-webmvc-core</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.tigon</groupId>
                <artifactId>tigon-webmvc-spring-boot</artifactId>
                <version>${goblin.version}</version>
            </dependency>
            <!-- /Tigon -->
            <!-- 挖财二方包 -->
            <dependency>
                <groupId>com.wacai</groupId>
                <artifactId>wacai-boot-starter-web</artifactId>
                <version>1.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--Lightning Netty-->
            <dependency>
                <groupId>com.wacai</groupId>
                <artifactId>spring-boot-starter-webapi</artifactId>
                <version>1.0.14</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javax.servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring.retry.version}</version>
            </dependency>
           <!-- <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>little-boy-api</artifactId>
                <version>${little.boy.api.version}</version>
            </dependency>-->
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>gbl-middleware-api</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>gbl-redis</artifactId>
                <version>1.0.5-SNAPSHOT</version><exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.4</version>
            </dependency>
            <dependency>
                <groupId>com.wacai</groupId>
                <artifactId>ninja-client</artifactId>
                <version>${ninja.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>gbl-kafka</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>
            <!--jasmine-->
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>gbl-jasmine</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo</artifactId>
                <version>3.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jboss.netty</groupId>
                        <artifactId>netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.wacai</groupId>
                        <artifactId>ninja-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- /挖财二方包 -->
            <!-- Spring -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <artifactId>spring-core</artifactId>
                <groupId>org.springframework</groupId>
                <version>${spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-jcl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-releasetrain</artifactId>
                <version>Kay-SR5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <!-- /Spring -->
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.22.0-GA</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>3.4.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.101tec</groupId>
                <artifactId>zkclient</artifactId>
                <version>0.10</version>
            </dependency>
            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>2.2.0.Final</version>
            </dependency>
            <!-- ElasticSearch, Dubbo, Genesys SDK均使用netty，统一使用此版本 -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.10.6.Final</version>
            </dependency>
            <!-- prometheus -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>0.12.0</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>0.12.0</version>
            </dependency>
            <!-- Wacai StrongBox, Redis使用了不同版本的Guava，使用此版本做兼容 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>
            <!-- Database -->
            <dependency>
                <groupId>com.wacai.platform</groupId>
                <artifactId>mybatis-cipher-plugin</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.4.5</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>6.0.6</version>
            </dependency>
            <!-- /Database -->
            <!-- JavaX -->
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>persistence-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet.jsp.jstl</groupId>
                <artifactId>jstl-api</artifactId>
                <version>1.2</version>
            </dependency>
            <!-- /JavaX -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.14</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.47</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <artifactId>commons-fileupload</artifactId>
                <groupId>commons-fileupload</groupId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>4.1.6</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.7.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.16</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.platform</groupId>
                <artifactId>prophet-spring</artifactId>
                <version>2.2.1-amc-FIXBUG</version>
            </dependency>
            <dependency>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.23</version>
            </dependency>
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.shardingsphere</groupId>
                <artifactId>sharding-jdbc-core</artifactId>
                <version>${sharding-sphere.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.middleware</groupId>
                <artifactId>guard-api-specification</artifactId>
                <version>1.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.wacai</groupId>
                <artifactId>wacai-boot-starter-registery</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.wacai.loan</groupId>
                <artifactId>tenant-spring-bean</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>
            
            <!-- 资管图片上传 -->
            <dependency>
				<groupId>com.wacai</groupId>
				<artifactId>file-storage-client</artifactId>
				<version>0.0.3</version>
			</dependency>

			<!-- 新告警平台 -->
            <dependency>
				<groupId>com.wacai</groupId>
				<artifactId>redalert-client</artifactId>
				<version>${redalert.client.version}</version>
			</dependency>
            <dependency>
                <artifactId>gbl-file-storage</artifactId>
                <groupId>com.wacai.loan</groupId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.coderplus.maven.plugins</groupId>
                <artifactId>copy-rename-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <addResources>true</addResources>
                        <mainClass>${start-class}</mainClass>
                        <layout>ZIP</layout>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.6.1</version>
                    <executions>
                        <execution>
                            <id>remove-config-file</id>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                            <phase>validate</phase>
                            <configuration>
                                <excludeDefaultDirectories>true</excludeDefaultDirectories>
                                <filesets>
                                    <fileset>
                                        <directory>src/main/resources/spring</directory>
                                        <includes>
                                            <include>config.properties</include>
                                        </includes>
                                    </fileset>
                                    <fileset>
                                        <directory>src/main/resources</directory>
                                        <includes>
                                            <include>application.properties</include>
                                            <include>application.yaml</include>
                                            <include>log4j2.xml</include>
                                            <include>logback.xml</include>
                                        </includes>
                                    </fileset>
                                </filesets>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.coderplus.maven.plugins</groupId>
                    <artifactId>copy-rename-maven-plugin</artifactId>
                    <version>1.0</version>
                    <executions>
                        <execution>
                            <id>copy-config-props-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/config_dev.properties</sourceFile>
                                <destinationFile>src/main/resources/spring/config.properties</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-application-props-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/application_dev.properties</sourceFile>
                                <destinationFile>src/main/resources/application.properties</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-application-yaml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/application_dev.yaml</sourceFile>
                                <destinationFile>src/main/resources/application.yaml</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-log4j2-xml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/log4j2_dev.xml</sourceFile>
                                <destinationFile>src/main/resources/log4j2.xml</destinationFile>
                            </configuration>
                        </execution>
                        <execution>
                            <id>copy-logback-xml-file</id>
                            <phase>generate-resources</phase>
                            <goals>
                                <goal>copy</goal>
                            </goals>
                            <configuration>
                                <sourceFile>src/main/resources/logback_dev.xml</sourceFile>
                                <destinationFile>src/main/resources/logback.xml</destinationFile>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <compilerArgs>
                            <!--<arg>-verbose</arg>-->
                            <!--<arg>-Xlint:all,-options,-path</arg>-->
                            <arg>-Xlint:unchecked</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>install</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.10</version>
                    <executions>
                        <execution>
                            <id>copy-dependencies</id>
                            <phase>package</phase>
                            <goals>
                                <goal>copy-dependencies</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>
                                    target/lib
                                </outputDirectory>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <excludes>
                            <exclude>*.properties</exclude>
                            <exclude>*.yml</exclude>
                            <exclude>*.yaml</exclude>
                            <exclude>**/logback.xml</exclude>
                            <exclude>**/log4j2.xml</exclude>
                        </excludes>
                        <archive>
                            <manifest>
                                <addClasspath>true</addClasspath>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                            <manifestEntries>
                                <Built-By>Wacai</Built-By>
                            </manifestEntries>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-clean-plugin</artifactId>
                                        <versionRange>
                                            [2.6.1,)
                                        </versionRange>
                                        <goals>
                                            <goal>clean</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.6.0</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>2.2.4</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <!-- filter resources -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*Mapper.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>config_*.properties</exclude>
                    <exclude>application_*.properties</exclude>
                    <exclude>application_*.yaml</exclude>
                    <exclude>log4j2_*.xml</exclude>
                    <exclude>logback_*.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.txt</include>
                </includes>
                <excludes>
                    <exclude>config_*.properties</exclude>
                    <exclude>application_*.properties</exclude>
                    <exclude>application_*.yaml</exclude>
                    <exclude>log4j2_*.xml</exclude>
                    <exclude>logback_*.xml</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>false</filtering>
            </testResource>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.txt</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </testResource>
        </testResources>
    </build>

    <profiles>
        <profile>
            <id>init-git-hooks</id>
            <activation>
                <file>
                    <exists>.git-hooks/init.sh</exists>
                </file>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-git-hooks</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <successCodes>
                                        <code>0</code>
                                        <code>1</code>
                                    </successCodes>
                                    <executable>bash</executable>
                                    <commandlineArgs>${project.basedir}/.git-hooks/init.sh</commandlineArgs>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
